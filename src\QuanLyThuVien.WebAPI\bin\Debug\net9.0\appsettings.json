{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=QuanLyThuVienDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "JwtSettings": {"SecretKey": "QuanLyThuVien_SuperSecretKey_2024_MinimumLength32Characters!@#$%^&*()", "Issuer": "QuanLyThuVien.WebAPI", "Audience": "QuanLyThuVien.Client", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}