using Microsoft.EntityFrameworkCore;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Domain.Entities;
using QuanLyThuVien.Infrastructure.Data;

namespace QuanLyThuVien.Infrastructure.Repositories;

public class MuonTraSachRepository : IMuonTraSachRepository
{
    private readonly QuanLyThuVienDbContext _context;

    public MuonTraSachRepository(QuanLyThuVienDbContext context)
    {
        _context = context;
    }

    public async Task<PagedResult<MuonTraSach>> GetAllAsync(MuonTraSachSearchDto searchDto)
    {
        var query = _context.MuonTraSach
            .Include(m => m.NguoiDung)
            .Include(m => m.Sach)
            .AsQueryable();

        // Apply filters
        if (searchDto.NguoiDungId.HasValue)
        {
            query = query.Where(m => m.NguoiDungId == searchDto.NguoiDungId.Value);
        }

        if (searchDto.SachId.HasValue)
        {
            query = query.Where(m => m.SachId == searchDto.SachId.Value);
        }

        if (!string.IsNullOrEmpty(searchDto.TrangThai))
        {
            query = query.Where(m => m.TrangThai == searchDto.TrangThai);
        }

        if (!string.IsNullOrEmpty(searchDto.TenNguoiDung))
        {
            query = query.Where(m => m.NguoiDung.HoTen.Contains(searchDto.TenNguoiDung));
        }

        if (!string.IsNullOrEmpty(searchDto.TenSach))
        {
            query = query.Where(m => m.Sach.TenSach.Contains(searchDto.TenSach));
        }

        if (searchDto.NgayMuonTu.HasValue)
        {
            query = query.Where(m => m.NgayMuon >= searchDto.NgayMuonTu.Value);
        }

        if (searchDto.NgayMuonDen.HasValue)
        {
            query = query.Where(m => m.NgayMuon <= searchDto.NgayMuonDen.Value);
        }

        if (searchDto.NgayHenTraTu.HasValue)
        {
            query = query.Where(m => m.NgayHenTra >= searchDto.NgayHenTraTu.Value);
        }

        if (searchDto.NgayHenTraDen.HasValue)
        {
            query = query.Where(m => m.NgayHenTra <= searchDto.NgayHenTraDen.Value);
        }

        if (searchDto.QuaHan.HasValue && searchDto.QuaHan.Value)
        {
            query = query.Where(m => m.TrangThai == "DangMuon" && m.NgayHenTra < DateTime.Now);
        }

        // Apply sorting
        query = searchDto.SortBy?.ToLower() switch
        {
            "ngaymuon" => searchDto.SortDescending ? query.OrderByDescending(m => m.NgayMuon) : query.OrderBy(m => m.NgayMuon),
            "ngayhentra" => searchDto.SortDescending ? query.OrderByDescending(m => m.NgayHenTra) : query.OrderBy(m => m.NgayHenTra),
            "ngaytrathucte" => searchDto.SortDescending ? query.OrderByDescending(m => m.NgayTraThucTe) : query.OrderBy(m => m.NgayTraThucTe),
            "tennguoidung" => searchDto.SortDescending ? query.OrderByDescending(m => m.NguoiDung.HoTen) : query.OrderBy(m => m.NguoiDung.HoTen),
            "tensach" => searchDto.SortDescending ? query.OrderByDescending(m => m.Sach.TenSach) : query.OrderBy(m => m.Sach.TenSach),
            "trangthai" => searchDto.SortDescending ? query.OrderByDescending(m => m.TrangThai) : query.OrderBy(m => m.TrangThai),
            _ => query.OrderByDescending(m => m.NgayMuon)
        };

        var totalCount = await query.CountAsync();

        var items = await query
            .Skip((searchDto.Page - 1) * searchDto.PageSize)
            .Take(searchDto.PageSize)
            .ToListAsync();

        return new PagedResult<MuonTraSach>
        {
            Items = items,
            TotalCount = totalCount,
            Page = searchDto.Page,
            PageSize = searchDto.PageSize
        };
    }

    public async Task<MuonTraSach?> GetByIdAsync(int id)
    {
        return await _context.MuonTraSach
            .Include(m => m.NguoiDung)
            .Include(m => m.Sach)
            .FirstOrDefaultAsync(m => m.Id == id);
    }

    public async Task<List<MuonTraSach>> GetByNguoiDungIdAsync(int nguoiDungId)
    {
        return await _context.MuonTraSach
            .Include(m => m.Sach)
            .Where(m => m.NguoiDungId == nguoiDungId)
            .OrderByDescending(m => m.NgayMuon)
            .ToListAsync();
    }

    public async Task<List<MuonTraSach>> GetBySachIdAsync(int sachId)
    {
        return await _context.MuonTraSach
            .Include(m => m.NguoiDung)
            .Where(m => m.SachId == sachId)
            .OrderByDescending(m => m.NgayMuon)
            .ToListAsync();
    }

    public async Task<List<MuonTraSach>> GetActiveBorrowingsByNguoiDungIdAsync(int nguoiDungId)
    {
        return await _context.MuonTraSach
            .Include(m => m.Sach)
            .Where(m => m.NguoiDungId == nguoiDungId && m.TrangThai == "DangMuon")
            .OrderBy(m => m.NgayHenTra)
            .ToListAsync();
    }

    public async Task<List<MuonTraSach>> GetOverdueBorrowingsAsync()
    {
        return await _context.MuonTraSach
            .Include(m => m.NguoiDung)
            .Include(m => m.Sach)
            .Where(m => m.TrangThai == "DangMuon" && m.NgayHenTra < DateTime.Now)
            .OrderBy(m => m.NgayHenTra)
            .ToListAsync();
    }

    public async Task<MuonTraSach> CreateAsync(MuonTraSach muonTraSach)
    {
        _context.MuonTraSach.Add(muonTraSach);
        await _context.SaveChangesAsync();
        return muonTraSach;
    }

    public async Task<MuonTraSach> UpdateAsync(MuonTraSach muonTraSach)
    {
        _context.Entry(muonTraSach).State = EntityState.Modified;
        await _context.SaveChangesAsync();
        return muonTraSach;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var muonTraSach = await _context.MuonTraSach.FindAsync(id);
        if (muonTraSach == null) return false;

        muonTraSach.DaXoa = true;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ExistsAsync(int id)
    {
        return await _context.MuonTraSach.AnyAsync(m => m.Id == id);
    }

    public async Task<bool> CanBorrowBookAsync(int nguoiDungId, int sachId)
    {
        // Check if user exists and is active
        var nguoiDung = await _context.NguoiDung.FindAsync(nguoiDungId);
        if (nguoiDung == null || !nguoiDung.TrangThaiHoatDong) return false;

        // Check if book exists and has available copies
        var sach = await _context.Sach.FindAsync(sachId);
        if (sach == null || sach.SoLuongConLai <= 0) return false;

        // Check if user already has this book borrowed
        var hasActiveBorrowing = await _context.MuonTraSach
            .AnyAsync(m => m.NguoiDungId == nguoiDungId && m.SachId == sachId && m.TrangThai == "DangMuon");

        return !hasActiveBorrowing;
    }

    public async Task<bool> HasActiveBorrowingAsync(int nguoiDungId, int sachId)
    {
        return await _context.MuonTraSach
            .AnyAsync(m => m.NguoiDungId == nguoiDungId && m.SachId == sachId && m.TrangThai == "DangMuon");
    }

    public async Task<int> GetActiveBorrowingsCountByNguoiDungAsync(int nguoiDungId)
    {
        return await _context.MuonTraSach
            .CountAsync(m => m.NguoiDungId == nguoiDungId && m.TrangThai == "DangMuon");
    }

    public async Task<ThongKeMuonTraDto> GetStatisticsAsync()
    {
        var tongSoLuotMuon = await _context.MuonTraSach.CountAsync();
        var soSachDangMuon = await _context.MuonTraSach.CountAsync(m => m.TrangThai == "DangMuon");
        var soSachDaTra = await _context.MuonTraSach.CountAsync(m => m.TrangThai == "DaTra");
        var soSachQuaHan = await _context.MuonTraSach
            .CountAsync(m => m.TrangThai == "DangMuon" && m.NgayHenTra < DateTime.Now);
        var tongTienPhat = await _context.MuonTraSach
            .Where(m => m.TienPhat.HasValue)
            .SumAsync(m => m.TienPhat!.Value);

        return new ThongKeMuonTraDto
        {
            TongSoLuotMuon = tongSoLuotMuon,
            SoSachDangMuon = soSachDangMuon,
            SoSachDaTra = soSachDaTra,
            SoSachQuaHan = soSachQuaHan,
            TongTienPhat = tongTienPhat
        };
    }

    public async Task<List<MuonTraSach>> GetBorrowingHistoryAsync(int nguoiDungId, int page = 1, int pageSize = 10)
    {
        return await _context.MuonTraSach
            .Include(m => m.Sach)
            .Where(m => m.NguoiDungId == nguoiDungId)
            .OrderByDescending(m => m.NgayMuon)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<bool> UpdateOverdueStatusAsync()
    {
        var overdueBorrowings = await _context.MuonTraSach
            .Where(m => m.TrangThai == "DangMuon" && m.NgayHenTra < DateTime.Now)
            .ToListAsync();

        foreach (var borrowing in overdueBorrowings)
        {
            borrowing.TrangThai = "QuaHan";
        }

        await _context.SaveChangesAsync();
        return overdueBorrowings.Any();
    }

    public async Task<int> GetCurrentBorrowingsCountAsync()
    {
        return await _context.MuonTraSach
            .Where(m => !m.DaXoa && m.TrangThai == "DangMuon")
            .CountAsync();
    }

    public async Task<int> GetOverdueBorrowingsCountAsync()
    {
        return await _context.MuonTraSach
            .Where(m => !m.DaXoa && m.TrangThai == "DangMuon" && m.NgayHenTra < DateTime.Now)
            .CountAsync();
    }

    public async Task<List<TopBookDto>> GetTopBorrowedBooksAsync(int count)
    {
        return await _context.MuonTraSach
            .Where(m => !m.DaXoa)
            .Include(m => m.Sach)
            .GroupBy(m => new { m.SachId, m.Sach!.TenSach, m.Sach.TacGia })
            .Select(g => new TopBookDto
            {
                Id = g.Key.SachId,
                TenSach = g.Key.TenSach,
                TacGia = g.Key.TacGia,
                BorrowCount = g.Count()
            })
            .OrderByDescending(x => x.BorrowCount)
            .Take(count)
            .ToListAsync();
    }

    public async Task<List<TopBorrowerDto>> GetTopBorrowersAsync(int count)
    {
        return await _context.MuonTraSach
            .Where(m => !m.DaXoa)
            .Include(m => m.NguoiDung)
            .GroupBy(m => new { m.NguoiDungId, m.NguoiDung!.HoTen, m.NguoiDung.Email })
            .Select(g => new TopBorrowerDto
            {
                Id = g.Key.NguoiDungId,
                HoTen = g.Key.HoTen,
                Email = g.Key.Email,
                BorrowCount = g.Count()
            })
            .OrderByDescending(x => x.BorrowCount)
            .Take(count)
            .ToListAsync();
    }

    public async Task<List<RecentActivityDto>> GetRecentActivitiesAsync(int count)
    {
        var borrowActivities = await _context.MuonTraSach
            .Where(m => !m.DaXoa)
            .Include(m => m.NguoiDung)
            .Include(m => m.Sach)
            .OrderByDescending(m => m.NgayTao)
            .Take(count)
            .Select(m => new RecentActivityDto
            {
                Id = m.Id,
                ActivityType = m.TrangThai == "DaTra" ? "RETURN" : "BORROW",
                UserName = m.NguoiDung!.HoTen,
                BookTitle = m.Sach!.TenSach,
                ActivityDate = m.TrangThai == "DaTra" ? (m.NgayTraThucTe ?? m.NgayTao) : m.NgayTao,
                Description = m.TrangThai == "DaTra"
                    ? $"{m.NguoiDung.HoTen} đã trả sách \"{m.Sach.TenSach}\""
                    : $"{m.NguoiDung.HoTen} đã mượn sách \"{m.Sach.TenSach}\""
            })
            .ToListAsync();

        return borrowActivities;
    }
}
