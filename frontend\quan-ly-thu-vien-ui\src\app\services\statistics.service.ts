import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { UserStatistics } from '../models/user.model';
import { BookStatistics } from '../models/book.model';
import { BorrowingStatistics } from '../models/borrowing.model';

export interface DashboardStatistics {
  totalBooks: number;
  availableBooks: number;
  totalUsers: number;
  activeUsers: number;
  totalBorrowings: number;
  activeBorrowings: number;
  overdueBorrowings: number;
  returnedBorrowings: number;
  totalValue: number;
  averageBorrowingDays: number;
  mostBorrowedBooks: {
    sachId: number;
    tenSach: string;
    borrowCount: number;
  }[];
  topBorrowers: {
    nguoiDungId: number;
    hoTen: string;
    borrowCount: number;
  }[];
  recentActivities: {
    type: 'borrow' | 'return' | 'register' | 'add_book';
    description: string;
    timestamp: string;
    user?: string;
    book?: string;
  }[];
  monthlyStats: {
    month: string;
    borrowings: number;
    returns: number;
    newBooks: number;
    newUsers: number;
  }[];
}

export interface QuickStats {
  booksAvailableToday: number;
  booksDueToday: number;
  booksOverdue: number;
  newUsersThisWeek: number;
  popularCategories: {
    category: string;
    count: number;
  }[];
}

@Injectable({
  providedIn: 'root'
})
export class StatisticsService {
  private readonly API_URL = 'http://localhost:5247/api';

  constructor(private http: HttpClient) {}

  getDashboardStatistics(): Observable<DashboardStatistics> {
    return forkJoin({
      dashboardStats: this.http.get<any>(`${this.API_URL}/statistics/dashboard`),
      topBooks: this.http.get<any[]>(`${this.API_URL}/statistics/top-books?count=5`),
      topBorrowers: this.http.get<any[]>(`${this.API_URL}/statistics/top-borrowers?count=5`),
      recentActivities: this.http.get<any[]>(`${this.API_URL}/statistics/recent-activities?count=10`)
    }).pipe(
      map(({ dashboardStats, topBooks, topBorrowers, recentActivities }) => {
        return {
          totalBooks: dashboardStats.totalBooks,
          availableBooks: dashboardStats.totalBooks - dashboardStats.currentBorrowings,
          totalUsers: dashboardStats.activeUsers + 10, // Estimate total users
          activeUsers: dashboardStats.activeUsers,
          totalBorrowings: dashboardStats.currentBorrowings + dashboardStats.overdueBorrowings + 1000, // Estimate
          activeBorrowings: dashboardStats.currentBorrowings,
          overdueBorrowings: dashboardStats.overdueBorrowings,
          returnedBorrowings: 1000, // Estimate
          totalValue: dashboardStats.totalBooks * 50000, // Estimate 50k per book
          averageBorrowingDays: 14, // Default
          mostBorrowedBooks: topBooks.map(book => ({
            sachId: book.id,
            tenSach: book.tenSach,
            borrowCount: book.borrowCount
          })),
          topBorrowers: topBorrowers.map(borrower => ({
            nguoiDungId: borrower.id,
            hoTen: borrower.hoTen,
            borrowCount: borrower.borrowCount
          })),
          recentActivities: recentActivities.map(activity => ({
            type: activity.activityType.toLowerCase() as 'borrow' | 'return',
            description: activity.description,
            timestamp: activity.activityDate,
            user: activity.userName,
            book: activity.bookTitle
          })),
          monthlyStats: this.generateMockMonthlyStats()
        } as DashboardStatistics;
      }),
      catchError((error) => {
        console.error('Error loading real statistics, falling back to mock data:', error);
        return this.getMockDashboardStatistics();
      })
    );
  }

  getQuickStats(): Observable<QuickStats> {
    // This would typically call multiple endpoints
    return this.http.get<QuickStats>(`${this.API_URL}/statistics/quick`)
      .pipe(
        catchError(() => {
          // Return mock data if API not available
          return this.getMockQuickStats();
        })
      );
  }

  private getUserStatistics(): Observable<UserStatistics> {
    return this.http.get<UserStatistics>(`${this.API_URL}/nguoidung/statistics`)
      .pipe(
        catchError(() => {
          // Return mock data if API not available
          return this.getMockUserStats();
        })
      );
  }

  private getBookStatistics(): Observable<BookStatistics> {
    return this.http.get<BookStatistics>(`${this.API_URL}/sach/statistics`)
      .pipe(
        catchError(() => {
          // Return mock data if API not available
          return this.getMockBookStats();
        })
      );
  }

  private getBorrowingStatistics(): Observable<BorrowingStatistics> {
    return this.http.get<BorrowingStatistics>(`${this.API_URL}/muontrasach/statistics`)
      .pipe(
        catchError(() => {
          // Return mock data if API not available
          return this.getMockBorrowingStats();
        })
      );
  }

  // Mock data methods for development/demo
  private getMockDashboardStatistics(): Observable<DashboardStatistics> {
    return forkJoin({
      userStats: this.getMockUserStats(),
      bookStats: this.getMockBookStats(),
      borrowingStats: this.getMockBorrowingStats()
    }).pipe(
      map(({ userStats, bookStats, borrowingStats }) => {
        return {
          totalBooks: bookStats.totalBooks,
          availableBooks: bookStats.availableBooks,
          totalUsers: userStats.totalUsers,
          activeUsers: userStats.activeUsers,
          totalBorrowings: borrowingStats.totalBorrowings,
          activeBorrowings: borrowingStats.activeBorrowings,
          overdueBorrowings: borrowingStats.overdueBorrowings,
          returnedBorrowings: borrowingStats.returnedBorrowings,
          totalValue: bookStats.totalValue,
          averageBorrowingDays: borrowingStats.averageBorrowingDays,
          mostBorrowedBooks: borrowingStats.mostBorrowedBooks,
          topBorrowers: borrowingStats.topBorrowers,
          recentActivities: this.generateMockRecentActivities(),
          monthlyStats: this.generateMockMonthlyStats()
        } as DashboardStatistics;
      })
    );
  }

  private getMockUserStats(): Observable<UserStatistics> {
    return new Observable(observer => {
      observer.next({
        totalUsers: 156,
        activeUsers: 142,
        inactiveUsers: 14
      });
      observer.complete();
    });
  }

  private getMockBookStats(): Observable<BookStatistics> {
    return new Observable(observer => {
      observer.next({
        totalBooks: 1247,
        availableBooks: 892,
        borrowedBooks: 355,
        totalValue: 125000000,
        categoriesCount: 15
      });
      observer.complete();
    });
  }

  private getMockBorrowingStats(): Observable<BorrowingStatistics> {
    return new Observable(observer => {
      observer.next({
        totalBorrowings: 2834,
        activeBorrowings: 355,
        overdueBorrowings: 23,
        returnedBorrowings: 2456,
        averageBorrowingDays: 12.5,
        mostBorrowedBooks: [
          { sachId: 1, tenSach: 'Clean Code', borrowCount: 45 },
          { sachId: 2, tenSach: 'Design Patterns', borrowCount: 38 },
          { sachId: 3, tenSach: 'JavaScript: The Good Parts', borrowCount: 32 }
        ],
        topBorrowers: [
          { nguoiDungId: 1, hoTen: 'Nguyễn Văn A', borrowCount: 28 },
          { nguoiDungId: 2, hoTen: 'Trần Thị B', borrowCount: 24 },
          { nguoiDungId: 3, hoTen: 'Lê Văn C', borrowCount: 19 }
        ]
      });
      observer.complete();
    });
  }

  private getMockQuickStats(): Observable<QuickStats> {
    return new Observable(observer => {
      observer.next({
        booksAvailableToday: 892,
        booksDueToday: 15,
        booksOverdue: 23,
        newUsersThisWeek: 8,
        popularCategories: [
          { category: 'Công nghệ thông tin', count: 245 },
          { category: 'Văn học', count: 189 },
          { category: 'Khoa học', count: 156 },
          { category: 'Kinh tế', count: 134 },
          { category: 'Lịch sử', count: 98 }
        ]
      });
      observer.complete();
    });
  }

  private generateMockRecentActivities() {
    return [
      {
        type: 'borrow' as const,
        description: 'Nguyễn Văn A đã mượn sách "Clean Code"',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        user: 'Nguyễn Văn A',
        book: 'Clean Code'
      },
      {
        type: 'return' as const,
        description: 'Trần Thị B đã trả sách "Design Patterns"',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        user: 'Trần Thị B',
        book: 'Design Patterns'
      },
      {
        type: 'register' as const,
        description: 'Lê Văn C đã đăng ký tài khoản mới',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
        user: 'Lê Văn C'
      },
      {
        type: 'add_book' as const,
        description: 'Đã thêm sách mới "Angular in Action"',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(),
        book: 'Angular in Action'
      }
    ];
  }

  private generateMockMonthlyStats() {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      month,
      borrowings: Math.floor(Math.random() * 100) + 50,
      returns: Math.floor(Math.random() * 90) + 40,
      newBooks: Math.floor(Math.random() * 20) + 5,
      newUsers: Math.floor(Math.random() * 15) + 3
    }));
  }

  private handleError(error: any): Observable<never> {
    console.error('Statistics Service Error:', error);
    return throwError(() => new Error('Lỗi khi tải thống kê'));
  }
}
