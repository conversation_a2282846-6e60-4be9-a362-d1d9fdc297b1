using System.ComponentModel.DataAnnotations;

namespace QuanLyThuVien.Application.DTOs;

public class NguoiDungDto
{
    public int Id { get; set; }
    public string HoTen { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? SoDienT<PERSON>ai { get; set; }
    public string? Dia<PERSON>hi { get; set; }
    public DateTime NgaySinh { get; set; }
    public string GioiTinh { get; set; } = string.Empty;
    public string SoCanCuoc { get; set; } = string.Empty;
    public DateTime NgayDangKy { get; set; }
    public bool TrangThaiHoatDong { get; set; }
    public string VaiTro { get; set; } = string.Empty;
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
}

public class CreateNguoiDungDto
{
    [Required(ErrorMessage = "Họ tên là bắt buộc")]
    [MaxLength(100, ErrorMessage = "Họ tên không được vượt quá 100 ký tự")]
    public string HoTen { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email là bắt buộc")]
    [EmailAddress(ErrorMessage = "Email không hợp lệ")]
    [MaxLength(100, ErrorMessage = "Email không được vượt quá 100 ký tự")]
    public string Email { get; set; } = string.Empty;

    [MaxLength(15, ErrorMessage = "Số điện thoại không được vượt quá 15 ký tự")]
    public string? SoDienThoai { get; set; }

    [MaxLength(200, ErrorMessage = "Địa chỉ không được vượt quá 200 ký tự")]
    public string? DiaChi { get; set; }

    [Required(ErrorMessage = "Ngày sinh là bắt buộc")]
    public DateTime NgaySinh { get; set; }

    [Required(ErrorMessage = "Giới tính là bắt buộc")]
    [MaxLength(10, ErrorMessage = "Giới tính không được vượt quá 10 ký tự")]
    public string GioiTinh { get; set; } = string.Empty;

    [Required(ErrorMessage = "Số căn cước là bắt buộc")]
    [MaxLength(20, ErrorMessage = "Số căn cước không được vượt quá 20 ký tự")]
    public string SoCanCuoc { get; set; } = string.Empty;

    [MaxLength(20, ErrorMessage = "Vai trò không được vượt quá 20 ký tự")]
    public string VaiTro { get; set; } = "User";

    [MinLength(6, ErrorMessage = "Mật khẩu phải có ít nhất 6 ký tự")]
    public string? MatKhau { get; set; }
}

public class UpdateNguoiDungDto
{
    [Required(ErrorMessage = "Họ tên là bắt buộc")]
    [MaxLength(100, ErrorMessage = "Họ tên không được vượt quá 100 ký tự")]
    public string HoTen { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email là bắt buộc")]
    [EmailAddress(ErrorMessage = "Email không hợp lệ")]
    [MaxLength(100, ErrorMessage = "Email không được vượt quá 100 ký tự")]
    public string Email { get; set; } = string.Empty;

    [MaxLength(15, ErrorMessage = "Số điện thoại không được vượt quá 15 ký tự")]
    public string? SoDienThoai { get; set; }

    [MaxLength(200, ErrorMessage = "Địa chỉ không được vượt quá 200 ký tự")]
    public string? DiaChi { get; set; }

    [Required(ErrorMessage = "Ngày sinh là bắt buộc")]
    public DateTime NgaySinh { get; set; }

    [Required(ErrorMessage = "Giới tính là bắt buộc")]
    [MaxLength(10, ErrorMessage = "Giới tính không được vượt quá 10 ký tự")]
    public string GioiTinh { get; set; } = string.Empty;

    [Required(ErrorMessage = "Số căn cước là bắt buộc")]
    [MaxLength(20, ErrorMessage = "Số căn cước không được vượt quá 20 ký tự")]
    public string SoCanCuoc { get; set; } = string.Empty;

    [MaxLength(20, ErrorMessage = "Vai trò không được vượt quá 20 ký tự")]
    public string VaiTro { get; set; } = "User";

    public bool TrangThaiHoatDong { get; set; } = true;
}

public class NguoiDungSearchDto
{
    public string? HoTen { get; set; }
    public string? Email { get; set; }
    public string? SoDienThoai { get; set; }
    public string? VaiTro { get; set; }
    public bool? TrangThaiHoatDong { get; set; }
    public DateTime? NgayDangKyTu { get; set; }
    public DateTime? NgayDangKyDen { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; } = "HoTen";
    public bool SortDescending { get; set; } = false;
}

public class ChangePasswordDto
{
    [Required(ErrorMessage = "Mật khẩu cũ là bắt buộc")]
    public string MatKhauCu { get; set; } = string.Empty;

    [Required(ErrorMessage = "Mật khẩu mới là bắt buộc")]
    [MinLength(6, ErrorMessage = "Mật khẩu mới phải có ít nhất 6 ký tự")]
    public string MatKhauMoi { get; set; } = string.Empty;

    [Required(ErrorMessage = "Xác nhận mật khẩu là bắt buộc")]
    [Compare("MatKhauMoi", ErrorMessage = "Mật khẩu xác nhận không khớp")]
    public string XacNhanMatKhau { get; set; } = string.Empty;
}
