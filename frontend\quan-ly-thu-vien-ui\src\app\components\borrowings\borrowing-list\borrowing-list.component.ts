import { Compo<PERSON>, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSortModule, MatSort, Sort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { Mat<PERSON>nackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';

import { BorrowingService } from '../../../services/borrowing.service';
import { AuthService } from '../../../services/auth.service';
import { Borrowing, BorrowingSearchRequest, BORROWING_STATUS_OPTIONS, SORT_OPTIONS, BorrowingStatus } from '../../../models/borrowing.model';
import { ReturnBookDialogComponent } from '../return-book-dialog/return-book-dialog.component';
import { ExtendBorrowingDialogComponent } from '../extend-borrowing-dialog/extend-borrowing-dialog.component';

@Component({
  selector: 'app-borrowing-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule
  ],
  template: `
    <div class="borrowing-list-container">
      <mat-card class="search-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>assignment</mat-icon>
            Quản lý mượn trả sách
          </mat-card-title>
          <div class="header-actions">
            <button mat-raised-button color="primary" routerLink="/borrowings/add">
              <mat-icon>assignment_add</mat-icon>
              Mượn sách mới
            </button>
            <button mat-raised-button color="warn" (click)="loadOverdueBooks()" [disabled]="isLoading">
              <mat-icon>warning</mat-icon>
              Sách quá hạn
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="searchForm" class="search-form">
            <div class="search-row">
              <mat-form-field appearance="outline">
                <mat-label>Tên người mượn</mat-label>
                <input matInput formControlName="hoTenNguoiDung" placeholder="Tìm theo tên">
                <mat-icon matSuffix>person</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Tên sách</mat-label>
                <input matInput formControlName="tenSach" placeholder="Tìm theo tên sách">
                <mat-icon matSuffix>book</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Trạng thái</mat-label>
                <mat-select formControlName="trangThai">
                  <mat-option value="">Tất cả</mat-option>
                  <mat-option *ngFor="let status of statusOptions" [value]="status.value">
                    {{ status.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Sắp xếp theo</mat-label>
                <mat-select formControlName="sortBy">
                  <mat-option *ngFor="let option of sortOptions" [value]="option.value">
                    {{ option.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="search-row">
              <mat-form-field appearance="outline">
                <mat-label>Ngày mượn từ</mat-label>
                <input matInput [matDatepicker]="fromPicker" formControlName="ngayMuonTu">
                <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
                <mat-datepicker #fromPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Ngày mượn đến</mat-label>
                <input matInput [matDatepicker]="toPicker" formControlName="ngayMuonDen">
                <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
                <mat-datepicker #toPicker></mat-datepicker>
              </mat-form-field>

              <div class="checkbox-field">
                <mat-checkbox formControlName="isOverdue">
                  Chỉ hiển thị sách quá hạn
                </mat-checkbox>
              </div>
            </div>

            <div class="search-actions">
              <button mat-raised-button color="primary" (click)="onSearch()" [disabled]="isLoading">
                <mat-icon>search</mat-icon>
                Tìm kiếm
              </button>
              <button mat-button (click)="onReset()" [disabled]="isLoading">
                <mat-icon>refresh</mat-icon>
                Đặt lại
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <mat-card class="table-card">
        <mat-card-content>
          <div class="table-container">
            <mat-table [dataSource]="dataSource" matSort class="borrowing-table">
              <ng-container matColumnDef="nguoiDung">
                <mat-header-cell *matHeaderCellDef mat-sort-header="hoTenNguoiDung">Người mượn</mat-header-cell>
                <mat-cell *matCellDef="let borrowing">
                  <div class="user-info">
                    <span class="user-name">{{ borrowing.nguoiDung?.hoTen }}</span>
                    <span class="user-email">{{ borrowing.nguoiDung?.email }}</span>
                  </div>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="sach">
                <mat-header-cell *matHeaderCellDef mat-sort-header="tenSach">Sách</mat-header-cell>
                <mat-cell *matCellDef="let borrowing">
                  <div class="book-info">
                    <span class="book-title">{{ borrowing.sach?.tenSach }}</span>
                    <span class="book-author">{{ borrowing.sach?.tacGia }}</span>
                  </div>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="ngayMuon">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Ngày mượn</mat-header-cell>
                <mat-cell *matCellDef="let borrowing">{{ formatDate(borrowing.ngayMuon) }}</mat-cell>
              </ng-container>

              <ng-container matColumnDef="ngayHenTra">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Ngày hẹn trả</mat-header-cell>
                <mat-cell *matCellDef="let borrowing">
                  <span [ngClass]="getDueDateClass(borrowing.ngayHenTra, borrowing.trangThai)">
                    {{ formatDate(borrowing.ngayHenTra) }}
                  </span>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="ngayTraThucTe">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Ngày trả</mat-header-cell>
                <mat-cell *matCellDef="let borrowing">
                  {{ borrowing.ngayTraThucTe ? formatDate(borrowing.ngayTraThucTe) : 'Chưa trả' }}
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="trangThai">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Trạng thái</mat-header-cell>
                <mat-cell *matCellDef="let borrowing">
                  <mat-chip [ngClass]="getStatusClass(borrowing.trangThai)">
                    {{ getStatusLabel(borrowing.trangThai) }}
                  </mat-chip>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="actions">
                <mat-header-cell *matHeaderCellDef>Thao tác</mat-header-cell>
                <mat-cell *matCellDef="let borrowing">
                  <div class="action-buttons">
                    <button mat-icon-button color="primary" 
                            [routerLink]="['/borrowings/edit', borrowing.id]"
                            matTooltip="Chỉnh sửa"
                            *ngIf="canEdit(borrowing)">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="accent" 
                            (click)="onReturn(borrowing)"
                            matTooltip="Trả sách"
                            *ngIf="canReturn(borrowing)">
                      <mat-icon>assignment_return</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" 
                            (click)="onExtend(borrowing)"
                            matTooltip="Gia hạn"
                            *ngIf="canExtend(borrowing)">
                      <mat-icon>schedule</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" 
                            (click)="onDelete(borrowing)"
                            matTooltip="Xóa"
                            *ngIf="canDelete(borrowing)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </mat-cell>
              </ng-container>

              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </mat-table>

            <div class="loading-container" *ngIf="isLoading">
              <mat-spinner diameter="50"></mat-spinner>
            </div>

            <div class="no-data" *ngIf="!isLoading && dataSource.data.length === 0">
              <mat-icon>assignment_outline</mat-icon>
              <p>Không tìm thấy giao dịch mượn sách nào</p>
            </div>
          </div>

          <mat-paginator #paginator
                         [length]="totalCount"
                         [pageSize]="pageSize"
                         [pageSizeOptions]="[5, 10, 25, 50]"
                         [showFirstLastButtons]="true"
                         (page)="onPageChange($event)">
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styleUrls: ['./borrowing-list.component.scss']
})
export class BorrowingListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  searchForm!: FormGroup;
  dataSource = new MatTableDataSource<Borrowing>([]);
  displayedColumns: string[] = ['nguoiDung', 'sach', 'ngayMuon', 'ngayHenTra', 'ngayTraThucTe', 'trangThai', 'actions'];
  
  isLoading = false;
  totalCount = 0;
  pageSize = 10;
  currentPage = 1;
  
  statusOptions = BORROWING_STATUS_OPTIONS;
  sortOptions = SORT_OPTIONS;

  constructor(
    private borrowingService: BorrowingService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadBorrowings();
  }

  private initializeForm(): void {
    this.searchForm = this.formBuilder.group({
      hoTenNguoiDung: [''],
      tenSach: [''],
      trangThai: [''],
      ngayMuonTu: [''],
      ngayMuonDen: [''],
      isOverdue: [false],
      sortBy: ['ngayMuon']
    });
  }

  loadBorrowings(): void {
    this.isLoading = true;
    
    const searchRequest: BorrowingSearchRequest = {
      ...this.searchForm.value,
      ngayMuonTu: this.searchForm.value.ngayMuonTu ? this.formatDateForApi(this.searchForm.value.ngayMuonTu) : undefined,
      ngayMuonDen: this.searchForm.value.ngayMuonDen ? this.formatDateForApi(this.searchForm.value.ngayMuonDen) : undefined,
      page: this.currentPage,
      pageSize: this.pageSize,
      sortBy: this.searchForm.value.sortBy || 'ngayMuon',
      sortDescending: this.sort?.direction === 'desc'
    };

    this.borrowingService.getBorrowings(searchRequest).subscribe({
      next: (result) => {
        this.dataSource.data = result.items;
        this.totalCount = result.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi tải danh sách mượn sách', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  loadOverdueBooks(): void {
    this.searchForm.patchValue({ 
      isOverdue: true,
      trangThai: BorrowingStatus.OVERDUE 
    });
    this.onSearch();
  }

  onSearch(): void {
    this.currentPage = 1;
    this.loadBorrowings();
  }

  onReset(): void {
    this.searchForm.reset();
    this.searchForm.patchValue({ sortBy: 'ngayMuon' });
    this.currentPage = 1;
    this.loadBorrowings();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadBorrowings();
  }

  onSortChange(sort: Sort): void {
    this.loadBorrowings();
  }

  onReturn(borrowing: Borrowing): void {
    const dialogRef = this.dialog.open(ReturnBookDialogComponent, {
      width: '600px',
      data: borrowing,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Refresh the list after successful return
        this.loadBorrowings();
      }
    });
  }

  onExtend(borrowing: Borrowing): void {
    const dialogRef = this.dialog.open(ExtendBorrowingDialogComponent, {
      width: '600px',
      data: borrowing,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Refresh the list after successful extension
        this.loadBorrowings();
      }
    });
  }

  onDelete(borrowing: Borrowing): void {
    if (confirm(`Bạn có chắc chắn muốn xóa giao dịch mượn sách này?`)) {
      this.borrowingService.deleteBorrowing(borrowing.id).subscribe({
        next: () => {
          this.snackBar.open('Xóa giao dịch thành công', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadBorrowings();
        },
        error: (error) => {
          this.snackBar.open(error.message || 'Lỗi khi xóa giao dịch', 'Đóng', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  canEdit(borrowing: Borrowing): boolean {
    return borrowing.trangThai === BorrowingStatus.BORROWED && this.authService.canManageBooks();
  }

  canReturn(borrowing: Borrowing): boolean {
    return borrowing.trangThai === BorrowingStatus.BORROWED || borrowing.trangThai === BorrowingStatus.OVERDUE;
  }

  canExtend(borrowing: Borrowing): boolean {
    return borrowing.trangThai === BorrowingStatus.BORROWED && this.authService.canManageBooks();
  }

  canDelete(borrowing: Borrowing): boolean {
    return this.authService.canManageBooks();
  }

  getStatusLabel(status: BorrowingStatus): string {
    const statusOption = this.statusOptions.find(s => s.value === status);
    return statusOption?.label || status;
  }

  getStatusClass(status: BorrowingStatus): string {
    const statusOption = this.statusOptions.find(s => s.value === status);
    return `status-${statusOption?.color || 'default'}`;
  }

  getDueDateClass(dueDate: string, status: BorrowingStatus): string {
    if (status === BorrowingStatus.RETURNED) return 'due-date-returned';
    
    const due = new Date(dueDate);
    const today = new Date();
    const diffDays = Math.ceil((due.getTime() - today.getTime()) / (1000 * 3600 * 24));
    
    if (diffDays < 0) return 'due-date-overdue';
    if (diffDays <= 3) return 'due-date-warning';
    return 'due-date-normal';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('vi-VN');
  }

  private formatDateForApi(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}
