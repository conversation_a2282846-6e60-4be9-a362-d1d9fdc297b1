using System.Security.Claims;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Interfaces;

public interface IJwtService
{
    string GenerateAccessToken(NguoiDung nguoiDung);
    string GenerateRefreshToken();
    ClaimsPrincipal? GetPrincipalFromExpiredToken(string token);
    bool ValidateToken(string token);
    DateTime GetTokenExpiration(string token);
    string? GetUserIdFromToken(string token);
    string? GetEmailFromToken(string token);
}
