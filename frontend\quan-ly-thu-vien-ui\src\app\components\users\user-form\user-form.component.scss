.user-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-card {
  width: 100%;
}

.form-card .mat-mdc-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.user-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-weight: 500;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 8px;
  }
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  &.two-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}

.full-width {
  width: 100%;
}

.checkbox-field {
  display: flex;
  align-items: center;
  height: 56px; // Match mat-form-field height
  padding: 0 12px;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.form-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  height: 48px;
}

.button-spinner {
  margin-right: 8px;
}

// Form field customization
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: 8px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .user-form-container {
    padding: 16px;
  }

  .form-row {
    &.two-columns {
      grid-template-columns: 1fr;
    }
  }

  .form-actions {
    flex-direction: column-reverse;
    
    button {
      width: 100%;
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .user-form-container {
    padding: 12px;
  }

  .form-card {
    .mat-mdc-card-content {
      padding: 16px;
    }
  }

  .form-section h3 {
    font-size: 16px;
  }
}

// Custom snackbar styles
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

// Loading state
.form-actions button[disabled] {
  opacity: 0.7;
}

// Form validation styles
.mat-mdc-form-field.mat-form-field-invalid {
  .mat-mdc-text-field-wrapper {
    border-color: #f44336;
  }
}

// Hover effects
.form-actions button:hover:not([disabled]) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}
