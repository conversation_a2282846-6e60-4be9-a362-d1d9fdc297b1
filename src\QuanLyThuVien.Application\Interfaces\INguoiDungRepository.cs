using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Interfaces;

public interface INguoiDungRepository
{
    Task<PagedResult<NguoiDung>> GetAllAsync(NguoiDungSearchDto searchDto);
    Task<NguoiDung?> GetByIdAsync(int id);
    Task<NguoiDung?> GetByEmailAsync(string email);
    Task<NguoiDung?> GetBySoCanCuocAsync(string soCanCuoc);
    Task<NguoiDung> CreateAsync(NguoiDung nguoiDung);
    Task<NguoiDung> UpdateAsync(NguoiDung nguoiDung);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
    Task<bool> IsEmailExistsAsync(string email, int? excludeId = null);
    Task<bool> IsSoCanCuocExistsAsync(string soCanCuoc, int? excludeId = null);
    Task<bool> CanDeleteAsync(int id);
    Task<List<NguoiDung>> GetActiveUsersAsync();
    Task<int> GetTotalUsersCountAsync();
    Task<int> GetActiveUsersCountAsync();
}
