.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.login-card mat-card-header {
  text-align: center;
  margin-bottom: 20px;
}

.login-card mat-card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.login-card mat-card-title mat-icon {
  font-size: 28px;
  color: #667eea;
}

.login-card mat-card-subtitle {
  color: #666;
  margin-top: 8px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
}

.form-actions {
  margin-top: 16px;
}

.login-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  position: relative;
}

.button-spinner {
  margin-right: 8px;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

.form-footer p {
  margin: 0;
  color: #666;
}

.form-footer button {
  font-weight: 500;
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-card {
    padding: 16px;
  }
  
  .login-card mat-card-title {
    font-size: 20px;
  }
}

// Custom snackbar styles
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

// Form field customization
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: 8px;
  }
}

// Button hover effects
.login-button:hover:not([disabled]) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;
}

// Loading state
.login-button[disabled] {
  opacity: 0.7;
}
