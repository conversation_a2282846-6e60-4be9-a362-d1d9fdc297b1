using Microsoft.EntityFrameworkCore;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Infrastructure.Data;

public class QuanLyThuVienDbContext : DbContext
{
    public QuanLyThuVienDbContext(DbContextOptions<QuanLyThuVienDbContext> options) : base(options)
    {
    }

    public DbSet<Sach> Sach { get; set; }
    public DbSet<NguoiDung> NguoiDung { get; set; }
    public DbSet<MuonTraSach> MuonTraSach { get; set; }
    public DbSet<RefreshToken> RefreshTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Cấu hình cho entity Sach
        modelBuilder.Entity<Sach>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TenSach).IsRequired().HasMaxLength(200);
            entity.Property(e => e.TacGia).HasMaxLength(100);
            entity.Property(e => e.NhaXuatBan).HasMaxLength(50);
            entity.Property(e => e.ISBN).HasMaxLength(20);
            entity.Property(e => e.TheLoai).HasMaxLength(50);
            entity.Property(e => e.MoTa).HasMaxLength(500);
            entity.Property(e => e.Gia).HasColumnType("decimal(18,2)");
            
            // Soft delete filter
            entity.HasQueryFilter(e => !e.DaXoa);
        });

        // Cấu hình cho entity NguoiDung
        modelBuilder.Entity<NguoiDung>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.HoTen).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
            entity.Property(e => e.SoDienThoai).HasMaxLength(15);
            entity.Property(e => e.DiaChi).HasMaxLength(200);
            entity.Property(e => e.GioiTinh).HasMaxLength(10);
            entity.Property(e => e.SoCanCuoc).HasMaxLength(20);
            entity.Property(e => e.VaiTro).HasMaxLength(20).HasDefaultValue("User");
            entity.Property(e => e.MatKhauHash).HasMaxLength(255);
            
            // Unique constraints
            entity.HasIndex(e => e.Email).IsUnique();
            entity.HasIndex(e => e.SoCanCuoc).IsUnique();
            
            // Soft delete filter
            entity.HasQueryFilter(e => !e.DaXoa);
        });

        // Cấu hình cho entity MuonTraSach
        modelBuilder.Entity<MuonTraSach>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TrangThai).HasMaxLength(20).HasDefaultValue("DangMuon");
            entity.Property(e => e.GhiChu).HasMaxLength(500);
            entity.Property(e => e.TienPhat).HasColumnType("decimal(18,2)");

            // Relationships
            entity.HasOne(e => e.NguoiDung)
                  .WithMany(n => n.DanhSachMuonTra)
                  .HasForeignKey(e => e.NguoiDungId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Sach)
                  .WithMany(s => s.DanhSachMuonTra)
                  .HasForeignKey(e => e.SachId)
                  .OnDelete(DeleteBehavior.Restrict);
            
            // Soft delete filter
            entity.HasQueryFilter(e => !e.DaXoa);
        });

        // Cấu hình cho entity RefreshToken
        modelBuilder.Entity<RefreshToken>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Token).IsRequired().HasMaxLength(500);
            entity.Property(e => e.CreatedByIp).HasMaxLength(50);
            entity.Property(e => e.RevokedByIp).HasMaxLength(50);
            entity.Property(e => e.ReplacedByToken).HasMaxLength(500);

            // Relationships
            entity.HasOne(e => e.NguoiDung)
                  .WithMany(n => n.RefreshTokens)
                  .HasForeignKey(e => e.NguoiDungId)
                  .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            entity.HasIndex(e => e.Token).IsUnique();
            entity.HasIndex(e => e.NguoiDungId);

            // Soft delete filter
            entity.HasQueryFilter(e => !e.DaXoa);
        });
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries<BaseEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.NgayTao = DateTime.Now;
                    break;
                case EntityState.Modified:
                    entry.Entity.NgayCapNhat = DateTime.Now;
                    break;
            }
        }
    }
}
