import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { BorrowingService } from '../../../services/borrowing.service';
import { Borrowing, DEFAULT_BORROWING_DAYS } from '../../../models/borrowing.model';

@Component({
  selector: 'app-extend-borrowing-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <div class="extend-dialog">
      <h2 mat-dialog-title>
        <mat-icon>schedule</mat-icon>
        Gia hạn mượn sách
      </h2>

      <mat-dialog-content>
        <div class="borrowing-info">
          <h3>Thông tin mượn sách</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>Người mượn:</label>
              <span>{{ borrowing.nguoiDung?.hoTen }}</span>
            </div>
            <div class="info-item">
              <label>Email:</label>
              <span>{{ borrowing.nguoiDung?.email }}</span>
            </div>
            <div class="info-item">
              <label>Sách:</label>
              <span>{{ borrowing.sach?.tenSach }}</span>
            </div>
            <div class="info-item">
              <label>Tác giả:</label>
              <span>{{ borrowing.sach?.tacGia }}</span>
            </div>
            <div class="info-item">
              <label>Ngày mượn:</label>
              <span>{{ formatDate(borrowing.ngayMuon) }}</span>
            </div>
            <div class="info-item">
              <label>Ngày hẹn trả hiện tại:</label>
              <span [ngClass]="getDueDateClass()">{{ formatDate(borrowing.ngayHenTra) }}</span>
            </div>
            <div class="info-item">
              <label>Số ngày đã mượn:</label>
              <span>{{ getCurrentBorrowingDays() }} ngày</span>
            </div>
            <div class="info-item" *ngIf="isOverdue()">
              <label>Số ngày quá hạn:</label>
              <span class="overdue-days">{{ getOverdueDays() }} ngày</span>
            </div>
          </div>
        </div>

        <form [formGroup]="extendForm" class="extend-form">
          <div class="extend-options">
            <h4>Chọn thời gian gia hạn</h4>
            <div class="option-buttons">
              <button mat-stroked-button type="button" 
                      (click)="setExtendDays(7)"
                      [class.selected]="getSelectedDays() === 7">
                7 ngày
              </button>
              <button mat-stroked-button type="button" 
                      (click)="setExtendDays(14)"
                      [class.selected]="getSelectedDays() === 14">
                14 ngày
              </button>
              <button mat-stroked-button type="button" 
                      (click)="setExtendDays(21)"
                      [class.selected]="getSelectedDays() === 21">
                21 ngày
              </button>
              <button mat-stroked-button type="button" 
                      (click)="setExtendDays(30)"
                      [class.selected]="getSelectedDays() === 30">
                30 ngày
              </button>
            </div>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Ngày hẹn trả mới *</mat-label>
            <input matInput [matDatepicker]="newDuePicker" formControlName="ngayHenTraMoi" [min]="minNewDueDate">
            <mat-datepicker-toggle matSuffix [for]="newDuePicker"></mat-datepicker-toggle>
            <mat-datepicker #newDuePicker></mat-datepicker>
            <mat-error *ngIf="extendForm.get('ngayHenTraMoi')?.invalid && extendForm.get('ngayHenTraMoi')?.touched">
              Ngày hẹn trả mới là bắt buộc và phải sau ngày hẹn trả hiện tại
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Lý do gia hạn</mat-label>
            <textarea matInput formControlName="lyDoGiaHan" rows="3" 
                      placeholder="Nhập lý do gia hạn mượn sách..."></textarea>
            <mat-icon matSuffix>notes</mat-icon>
          </mat-form-field>

          <div class="extend-summary" *ngIf="extendForm.get('ngayHenTraMoi')?.value">
            <h4>Tóm tắt gia hạn</h4>
            <div class="summary-grid">
              <div class="summary-item">
                <label>Thời gian gia hạn:</label>
                <span class="extend-days">{{ getExtendDays() }} ngày</span>
              </div>
              <div class="summary-item">
                <label>Tổng thời gian mượn:</label>
                <span>{{ getTotalBorrowingDays() }} ngày</span>
              </div>
              <div class="summary-item">
                <label>Ngày hẹn trả mới:</label>
                <span class="new-due-date">{{ formatDate(extendForm.value.ngayHenTraMoi) }}</span>
              </div>
            </div>
          </div>
        </form>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button mat-button (click)="onCancel()" [disabled]="isLoading">
          <mat-icon>cancel</mat-icon>
          Hủy
        </button>
        <button mat-raised-button color="primary" (click)="onExtend()" 
                [disabled]="extendForm.invalid || isLoading">
          <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
          <mat-icon *ngIf="!isLoading">schedule</mat-icon>
          {{ isLoading ? 'Đang xử lý...' : 'Gia hạn' }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .extend-dialog {
      width: 600px;
      max-width: 90vw;
    }

    h2[mat-dialog-title] {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 16px 0;
      color: #333;
    }

    .borrowing-info {
      background-color: #f5f5f5;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 20px;

      h3 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      label {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }

      span {
        color: #333;
        font-weight: 400;
      }

      .overdue-days {
        color: #f44336;
        font-weight: 600;
      }
    }

    .due-date-overdue {
      color: #f44336;
      font-weight: 600;
    }

    .due-date-warning {
      color: #ff9800;
      font-weight: 500;
    }

    .due-date-normal {
      color: #333;
    }

    .extend-form {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .extend-options {
      h4 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 14px;
      }
    }

    .option-buttons {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;

      button {
        height: 40px;
        border: 2px solid #e0e0e0;
        color: #666;
        transition: all 0.2s ease;

        &:hover {
          border-color: #2196f3;
          color: #2196f3;
        }

        &.selected {
          border-color: #2196f3;
          background-color: #e3f2fd;
          color: #1976d2;
          font-weight: 500;
        }
      }
    }

    .full-width {
      width: 100%;
    }

    .extend-summary {
      background-color: #fff3e0;
      padding: 16px;
      border-radius: 8px;
      border-left: 4px solid #ff9800;

      h4 {
        margin: 0 0 12px 0;
        color: #f57c00;
        font-size: 14px;
      }
    }

    .summary-grid {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      label {
        font-size: 13px;
        color: #555;
      }

      span {
        font-weight: 500;
        
        &.extend-days {
          color: #ff9800;
        }
        
        &.new-due-date {
          color: #2196f3;
        }
      }
    }

    mat-dialog-actions {
      margin-top: 20px;
      padding: 16px 0 0 0;
      border-top: 1px solid #e0e0e0;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 100px;
      }
    }

    .button-spinner {
      margin-right: 8px;
    }

    @media (max-width: 768px) {
      .extend-dialog {
        width: 100%;
        max-width: 100vw;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      .option-buttons {
        grid-template-columns: repeat(2, 1fr);
      }

      mat-dialog-actions {
        flex-direction: column-reverse;
        gap: 8px;
        
        button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  `]
})
export class ExtendBorrowingDialogComponent implements OnInit {
  extendForm!: FormGroup;
  isLoading = false;
  minNewDueDate: Date;

  constructor(
    public dialogRef: MatDialogRef<ExtendBorrowingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public borrowing: Borrowing,
    private formBuilder: FormBuilder,
    private borrowingService: BorrowingService,
    private snackBar: MatSnackBar
  ) {
    // Set minimum new due date to current due date + 1 day
    this.minNewDueDate = new Date(borrowing.ngayHenTra);
    this.minNewDueDate.setDate(this.minNewDueDate.getDate() + 1);
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    const defaultNewDueDate = new Date(this.borrowing.ngayHenTra);
    defaultNewDueDate.setDate(defaultNewDueDate.getDate() + DEFAULT_BORROWING_DAYS);

    this.extendForm = this.formBuilder.group({
      ngayHenTraMoi: [defaultNewDueDate, [Validators.required]],
      lyDoGiaHan: ['', [Validators.maxLength(500)]]
    });
  }

  setExtendDays(days: number): void {
    const currentDueDate = new Date(this.borrowing.ngayHenTra);
    const newDueDate = new Date(currentDueDate);
    newDueDate.setDate(currentDueDate.getDate() + days);
    
    this.extendForm.patchValue({
      ngayHenTraMoi: newDueDate
    });
  }

  getSelectedDays(): number {
    const currentDueDate = new Date(this.borrowing.ngayHenTra);
    const newDueDate = this.extendForm.value.ngayHenTraMoi;
    
    if (!newDueDate) return 0;
    
    const diffTime = newDueDate.getTime() - currentDueDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  onExtend(): void {
    if (this.extendForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      const newDueDate = this.extendForm.value.ngayHenTraMoi.toISOString().split('T')[0];

      this.borrowingService.extendBorrowing(this.borrowing.id, newDueDate).subscribe({
        next: (updatedBorrowing) => {
          this.isLoading = false;
          this.snackBar.open('Gia hạn mượn sách thành công!', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.dialogRef.close(updatedBorrowing);
        },
        error: (error) => {
          this.isLoading = false;
          this.snackBar.open(error.message || 'Lỗi khi gia hạn mượn sách', 'Đóng', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  formatDate(dateString: string | Date): string {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('vi-VN');
  }

  getCurrentBorrowingDays(): number {
    const borrowDate = new Date(this.borrowing.ngayMuon);
    const today = new Date();
    return Math.ceil((today.getTime() - borrowDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  getExtendDays(): number {
    const currentDueDate = new Date(this.borrowing.ngayHenTra);
    const newDueDate = this.extendForm.value.ngayHenTraMoi;
    
    if (!newDueDate) return 0;
    
    const diffTime = newDueDate.getTime() - currentDueDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getTotalBorrowingDays(): number {
    const borrowDate = new Date(this.borrowing.ngayMuon);
    const newDueDate = this.extendForm.value.ngayHenTraMoi;
    
    if (!newDueDate) return 0;
    
    const diffTime = newDueDate.getTime() - borrowDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  isOverdue(): boolean {
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const today = new Date();
    return today > dueDate;
  }

  getOverdueDays(): number {
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const today = new Date();
    return Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  getDueDateClass(): string {
    if (this.isOverdue()) return 'due-date-overdue';
    
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const today = new Date();
    const diffDays = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 3) return 'due-date-warning';
    return 'due-date-normal';
  }
}
