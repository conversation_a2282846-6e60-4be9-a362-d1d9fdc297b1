import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { AuthService } from '../../services/auth.service';
import { UserInfo, ChangePasswordRequest } from '../../models/auth.model';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatSnackBarModule
  ],
  template: `
    <div class="profile-container">
      <mat-card class="profile-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>person</mat-icon>
            Hồ sơ cá nhân
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <mat-tab-group>
            <mat-tab label="Thông tin cá nhân">
              <div class="tab-content" *ngIf="currentUser">
                <div class="user-info">
                  <div class="info-item">
                    <label>Họ và tên:</label>
                    <span>{{ currentUser.hoTen }}</span>
                  </div>
                  <div class="info-item">
                    <label>Email:</label>
                    <span>{{ currentUser.email }}</span>
                  </div>
                  <div class="info-item">
                    <label>Vai trò:</label>
                    <span class="role-badge" [ngClass]="getRoleClass(currentUser.vaiTro)">
                      {{ getRoleDisplayName(currentUser.vaiTro) }}
                    </span>
                  </div>
                  <div class="info-item">
                    <label>Trạng thái:</label>
                    <span class="status-badge" [ngClass]="currentUser.trangThaiHoatDong ? 'active' : 'inactive'">
                      {{ currentUser.trangThaiHoatDong ? 'Hoạt động' : 'Không hoạt động' }}
                    </span>
                  </div>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Đổi mật khẩu">
              <div class="tab-content">
                <form [formGroup]="passwordForm" (ngSubmit)="onChangePassword()" class="password-form">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Mật khẩu hiện tại</mat-label>
                    <input matInput [type]="hideCurrentPassword ? 'password' : 'text'" formControlName="currentPassword">
                    <button mat-icon-button matSuffix type="button" (click)="toggleCurrentPasswordVisibility()">
                      <mat-icon>{{ hideCurrentPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                    </button>
                    <mat-error *ngIf="passwordForm.get('currentPassword')?.invalid && passwordForm.get('currentPassword')?.touched">
                      Mật khẩu hiện tại là bắt buộc
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Mật khẩu mới</mat-label>
                    <input matInput [type]="hideNewPassword ? 'password' : 'text'" formControlName="newPassword">
                    <button mat-icon-button matSuffix type="button" (click)="toggleNewPasswordVisibility()">
                      <mat-icon>{{ hideNewPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                    </button>
                    <mat-error *ngIf="passwordForm.get('newPassword')?.invalid && passwordForm.get('newPassword')?.touched">
                      Mật khẩu mới phải có ít nhất 6 ký tự
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Xác nhận mật khẩu mới</mat-label>
                    <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmNewPassword">
                    <button mat-icon-button matSuffix type="button" (click)="toggleConfirmPasswordVisibility()">
                      <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                    </button>
                    <mat-error *ngIf="passwordForm.hasError('passwordMismatch') && passwordForm.get('confirmNewPassword')?.touched">
                      Mật khẩu xác nhận không khớp
                    </mat-error>
                  </mat-form-field>

                  <div class="form-actions">
                    <button mat-raised-button color="primary" type="submit" [disabled]="passwordForm.invalid || isChangingPassword">
                      <mat-icon>save</mat-icon>
                      {{ isChangingPassword ? 'Đang cập nhật...' : 'Đổi mật khẩu' }}
                    </button>
                  </div>
                </form>
              </div>
            </mat-tab>
          </mat-tab-group>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .profile-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .profile-card {
      width: 100%;
    }

    .profile-card mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .tab-content {
      padding: 20px 0;
    }

    .user-info {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #eee;
    }

    .info-item label {
      font-weight: 500;
      min-width: 120px;
      color: #666;
    }

    .info-item span {
      flex: 1;
    }

    .role-badge, .status-badge {
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
    }

    .role-badge.admin {
      background-color: #f44336;
      color: white;
    }

    .role-badge.librarian {
      background-color: #ff9800;
      color: white;
    }

    .role-badge.user {
      background-color: #4caf50;
      color: white;
    }

    .status-badge.active {
      background-color: #4caf50;
      color: white;
    }

    .status-badge.inactive {
      background-color: #9e9e9e;
      color: white;
    }

    .password-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      max-width: 400px;
    }

    .full-width {
      width: 100%;
    }

    .form-actions {
      margin-top: 16px;
    }

    .form-actions button {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .profile-container {
        padding: 16px;
      }
      
      .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
      
      .info-item label {
        min-width: auto;
      }
    }
  `]
})
export class ProfileComponent implements OnInit {
  currentUser: UserInfo | null = null;
  passwordForm!: FormGroup;
  isChangingPassword = false;
  hideCurrentPassword = true;
  hideNewPassword = true;
  hideConfirmPassword = true;

  constructor(
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializePasswordForm();
    this.loadCurrentUser();
  }

  private initializePasswordForm(): void {
    this.passwordForm = this.formBuilder.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmNewPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  private passwordMatchValidator(group: any): { [key: string]: any } | null {
    const newPassword = group.get('newPassword');
    const confirmNewPassword = group.get('confirmNewPassword');
    
    if (!newPassword || !confirmNewPassword) return null;
    
    return newPassword.value === confirmNewPassword.value ? null : { passwordMismatch: true };
  }

  private loadCurrentUser(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  onChangePassword(): void {
    if (this.passwordForm.valid && !this.isChangingPassword) {
      this.isChangingPassword = true;
      
      const passwordData: ChangePasswordRequest = {
        currentPassword: this.passwordForm.value.currentPassword,
        newPassword: this.passwordForm.value.newPassword,
        confirmNewPassword: this.passwordForm.value.confirmNewPassword
      };

      this.authService.changePassword(passwordData).subscribe({
        next: () => {
          this.isChangingPassword = false;
          this.passwordForm.reset();
          this.snackBar.open('Đổi mật khẩu thành công!', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          this.isChangingPassword = false;
          this.snackBar.open(error.message || 'Đổi mật khẩu thất bại!', 'Đóng', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  getRoleDisplayName(role: string): string {
    const roleNames: { [key: string]: string } = {
      'Admin': 'Quản trị viên',
      'Librarian': 'Thủ thư',
      'User': 'Người dùng'
    };
    return roleNames[role] || role;
  }

  getRoleClass(role: string): string {
    return role.toLowerCase();
  }

  toggleCurrentPasswordVisibility(): void {
    this.hideCurrentPassword = !this.hideCurrentPassword;
  }

  toggleNewPasswordVisibility(): void {
    this.hideNewPassword = !this.hideNewPassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.hideConfirmPassword = !this.hideConfirmPassword;
  }
}
