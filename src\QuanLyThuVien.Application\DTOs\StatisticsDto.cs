namespace QuanLyThuVien.Application.DTOs;

public class DashboardStatisticsDto
{
    public int TotalBooks { get; set; }
    public int ActiveUsers { get; set; }
    public int CurrentBorrowings { get; set; }
    public int OverdueBorrowings { get; set; }
}

public class TopBookDto
{
    public int Id { get; set; }
    public string TenSach { get; set; } = string.Empty;
    public string Tac<PERSON>ia { get; set; } = string.Empty;
    public int BorrowCount { get; set; }
}

public class TopBorrowerDto
{
    public int Id { get; set; }
    public string HoTen { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int BorrowCount { get; set; }
}

public class RecentActivityDto
{
    public int Id { get; set; }
    public string ActivityType { get; set; } = string.Empty; // "BORROW", "RETURN", "EXTEND"
    public string UserName { get; set; } = string.Empty;
    public string BookTitle { get; set; } = string.Empty;
    public DateTime ActivityDate { get; set; }
    public string Description { get; set; } = string.Empty;
}
