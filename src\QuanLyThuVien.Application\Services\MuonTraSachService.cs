using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Services;

public class MuonTraSachService : IMuonTraSachService
{
    private readonly IMuonTraSachRepository _muonTraSachRepository;
    private readonly ISachRepository _sachRepository;
    private readonly INguoiDungRepository _nguoiDungRepository;
    private const int MAX_BORROWING_DAYS = 30; // Maximum borrowing period
    private const int MAX_BOOKS_PER_USER = 5; // Maximum books per user
    private const decimal FINE_PER_DAY = 5000; // Fine per day in VND

    public MuonTraSachService(
        IMuonTraSachRepository muonTraSachRepository,
        ISachRepository sachRepository,
        INguoiDungRepository nguoiDungRepository)
    {
        _muonTraSachRepository = muonTraSachRepository;
        _sachRepository = sachRepository;
        _nguoiDungRepository = nguoiDungRepository;
    }

    public async Task<PagedResult<MuonTraSachDetailDto>> GetAllAsync(MuonTraSachSearchDto searchDto)
    {
        var result = await _muonTraSachRepository.GetAllAsync(searchDto);
        
        return new PagedResult<MuonTraSachDetailDto>
        {
            Items = result.Items.Select(MapToDetailDto).ToList(),
            TotalCount = result.TotalCount,
            Page = result.Page,
            PageSize = result.PageSize
        };
    }

    public async Task<MuonTraSachDetailDto?> GetByIdAsync(int id)
    {
        var muonTraSach = await _muonTraSachRepository.GetByIdAsync(id);
        return muonTraSach != null ? MapToDetailDto(muonTraSach) : null;
    }

    public async Task<List<MuonTraSachDetailDto>> GetByNguoiDungIdAsync(int nguoiDungId)
    {
        var muonTraSachList = await _muonTraSachRepository.GetByNguoiDungIdAsync(nguoiDungId);
        return muonTraSachList.Select(MapToDetailDto).ToList();
    }

    public async Task<List<MuonTraSachDetailDto>> GetActiveBorrowingsByNguoiDungIdAsync(int nguoiDungId)
    {
        var muonTraSachList = await _muonTraSachRepository.GetActiveBorrowingsByNguoiDungIdAsync(nguoiDungId);
        return muonTraSachList.Select(MapToDetailDto).ToList();
    }

    public async Task<List<MuonTraSachDetailDto>> GetOverdueBorrowingsAsync()
    {
        var muonTraSachList = await _muonTraSachRepository.GetOverdueBorrowingsAsync();
        return muonTraSachList.Select(MapToDetailDto).ToList();
    }

    public async Task<MuonTraSachDetailDto> MuonSachAsync(CreateMuonTraSachDto createDto)
    {
        // Validate user exists and is active
        var nguoiDung = await _nguoiDungRepository.GetByIdAsync(createDto.NguoiDungId);
        if (nguoiDung == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy người dùng với ID: {createDto.NguoiDungId}");
        }

        if (!nguoiDung.TrangThaiHoatDong)
        {
            throw new InvalidOperationException("Người dùng đã bị vô hiệu hóa, không thể mượn sách.");
        }

        // Validate book exists and has available copies
        var sach = await _sachRepository.GetByIdAsync(createDto.SachId);
        if (sach == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy sách với ID: {createDto.SachId}");
        }

        if (sach.SoLuongConLai <= 0)
        {
            throw new InvalidOperationException("Sách đã hết, không thể mượn.");
        }

        // Check if user can borrow this book
        if (!await _muonTraSachRepository.CanBorrowBookAsync(createDto.NguoiDungId, createDto.SachId))
        {
            throw new InvalidOperationException("Người dùng đã mượn sách này hoặc không đủ điều kiện mượn.");
        }

        // Check borrowing limit
        var activeBorrowingsCount = await _muonTraSachRepository.GetActiveBorrowingsCountByNguoiDungAsync(createDto.NguoiDungId);
        if (activeBorrowingsCount >= MAX_BOOKS_PER_USER)
        {
            throw new InvalidOperationException($"Người dùng đã mượn tối đa {MAX_BOOKS_PER_USER} cuốn sách.");
        }

        // Validate return date
        if (createDto.NgayHenTra <= DateTime.Now)
        {
            throw new InvalidOperationException("Ngày hẹn trả phải sau ngày hiện tại.");
        }

        if (createDto.NgayHenTra > DateTime.Now.AddDays(MAX_BORROWING_DAYS))
        {
            throw new InvalidOperationException($"Thời gian mượn không được vượt quá {MAX_BORROWING_DAYS} ngày.");
        }

        // Create borrowing record
        var muonTraSach = new MuonTraSach
        {
            NguoiDungId = createDto.NguoiDungId,
            SachId = createDto.SachId,
            NgayMuon = DateTime.Now,
            NgayHenTra = createDto.NgayHenTra,
            TrangThai = "DangMuon",
            GhiChu = createDto.GhiChu
        };

        var createdMuonTraSach = await _muonTraSachRepository.CreateAsync(muonTraSach);

        // Update book quantity
        sach.SoLuongConLai--;
        await _sachRepository.UpdateAsync(sach);

        // Load navigation properties
        var result = await _muonTraSachRepository.GetByIdAsync(createdMuonTraSach.Id);
        return MapToDetailDto(result!);
    }

    public async Task<MuonTraSachDetailDto> TraSachAsync(TraSachDto traSachDto)
    {
        var muonTraSach = await _muonTraSachRepository.GetByIdAsync(traSachDto.MuonTraSachId);
        if (muonTraSach == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy bản ghi mượn sách với ID: {traSachDto.MuonTraSachId}");
        }

        if (muonTraSach.TrangThai != "DangMuon" && muonTraSach.TrangThai != "QuaHan")
        {
            throw new InvalidOperationException("Sách đã được trả hoặc trạng thái không hợp lệ.");
        }

        // Update borrowing record
        muonTraSach.NgayTraThucTe = traSachDto.NgayTraThucTe;
        muonTraSach.TrangThai = "DaTra";
        muonTraSach.GhiChu = traSachDto.GhiChu;
        muonTraSach.TienPhat = traSachDto.TienPhat ?? await CalculateFineAsync(traSachDto.MuonTraSachId);

        var updatedMuonTraSach = await _muonTraSachRepository.UpdateAsync(muonTraSach);

        // Update book quantity
        var sach = await _sachRepository.GetByIdAsync(muonTraSach.SachId);
        if (sach != null)
        {
            sach.SoLuongConLai++;
            await _sachRepository.UpdateAsync(sach);
        }

        // Load navigation properties
        var result = await _muonTraSachRepository.GetByIdAsync(updatedMuonTraSach.Id);
        return MapToDetailDto(result!);
    }

    public async Task<MuonTraSachDetailDto> UpdateAsync(int id, UpdateMuonTraSachDto updateDto)
    {
        var existingMuonTraSach = await _muonTraSachRepository.GetByIdAsync(id);
        if (existingMuonTraSach == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy bản ghi mượn sách với ID: {id}");
        }

        if (existingMuonTraSach.TrangThai == "DaTra")
        {
            throw new InvalidOperationException("Không thể cập nhật bản ghi đã trả sách.");
        }

        // Validate return date
        if (updateDto.NgayHenTra <= existingMuonTraSach.NgayMuon)
        {
            throw new InvalidOperationException("Ngày hẹn trả phải sau ngày mượn.");
        }

        if (updateDto.NgayHenTra > existingMuonTraSach.NgayMuon.AddDays(MAX_BORROWING_DAYS))
        {
            throw new InvalidOperationException($"Thời gian mượn không được vượt quá {MAX_BORROWING_DAYS} ngày.");
        }

        // Update properties
        existingMuonTraSach.NgayHenTra = updateDto.NgayHenTra;
        existingMuonTraSach.GhiChu = updateDto.GhiChu;

        var updatedMuonTraSach = await _muonTraSachRepository.UpdateAsync(existingMuonTraSach);

        // Load navigation properties
        var result = await _muonTraSachRepository.GetByIdAsync(updatedMuonTraSach.Id);
        return MapToDetailDto(result!);
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var muonTraSach = await _muonTraSachRepository.GetByIdAsync(id);
        if (muonTraSach == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy bản ghi mượn sách với ID: {id}");
        }

        if (muonTraSach.TrangThai == "DangMuon" || muonTraSach.TrangThai == "QuaHan")
        {
            throw new InvalidOperationException("Không thể xóa bản ghi sách đang được mượn.");
        }

        return await _muonTraSachRepository.DeleteAsync(id);
    }

    public async Task<bool> CanBorrowBookAsync(int nguoiDungId, int sachId)
    {
        return await _muonTraSachRepository.CanBorrowBookAsync(nguoiDungId, sachId);
    }

    public async Task<ThongKeMuonTraDto> GetStatisticsAsync()
    {
        return await _muonTraSachRepository.GetStatisticsAsync();
    }

    public async Task<List<MuonTraSachDetailDto>> GetBorrowingHistoryAsync(int nguoiDungId, int page = 1, int pageSize = 10)
    {
        var muonTraSachList = await _muonTraSachRepository.GetBorrowingHistoryAsync(nguoiDungId, page, pageSize);
        return muonTraSachList.Select(MapToDetailDto).ToList();
    }

    public async Task<bool> UpdateOverdueStatusAsync()
    {
        return await _muonTraSachRepository.UpdateOverdueStatusAsync();
    }

    public async Task<decimal> CalculateFineAsync(int muonTraSachId)
    {
        var muonTraSach = await _muonTraSachRepository.GetByIdAsync(muonTraSachId);
        if (muonTraSach == null) return 0;

        var returnDate = muonTraSach.NgayTraThucTe ?? DateTime.Now;
        if (returnDate <= muonTraSach.NgayHenTra) return 0;

        var overdueDays = (returnDate - muonTraSach.NgayHenTra).Days;
        return overdueDays * FINE_PER_DAY;
    }

    private static MuonTraSachDetailDto MapToDetailDto(MuonTraSach muonTraSach)
    {
        return new MuonTraSachDetailDto
        {
            Id = muonTraSach.Id,
            NguoiDungId = muonTraSach.NguoiDungId,
            SachId = muonTraSach.SachId,
            NgayMuon = muonTraSach.NgayMuon,
            NgayHenTra = muonTraSach.NgayHenTra,
            NgayTraThucTe = muonTraSach.NgayTraThucTe,
            TrangThai = muonTraSach.TrangThai,
            GhiChu = muonTraSach.GhiChu,
            TienPhat = muonTraSach.TienPhat,
            NgayTao = muonTraSach.NgayTao,
            NgayCapNhat = muonTraSach.NgayCapNhat,
            TenNguoiDung = muonTraSach.NguoiDung?.HoTen ?? "",
            EmailNguoiDung = muonTraSach.NguoiDung?.Email ?? "",
            SoDienThoaiNguoiDung = muonTraSach.NguoiDung?.SoDienThoai ?? "",
            TenSach = muonTraSach.Sach?.TenSach ?? "",
            TacGia = muonTraSach.Sach?.TacGia ?? "",
            ISBN = muonTraSach.Sach?.ISBN ?? ""
        };
    }
}
