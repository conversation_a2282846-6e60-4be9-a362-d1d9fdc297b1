import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { UserService } from '../../../services/user.service';
import { AuthService } from '../../../services/auth.service';
import { User, CreateUserRequest, UpdateUserRequest, USER_ROLES, GENDER_OPTIONS } from '../../../models/user.model';

@Component({
  selector: 'app-user-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <div class="user-form-container">
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>{{ isEditMode ? 'edit' : 'person_add' }}</mat-icon>
            {{ isEditMode ? 'Chỉnh sửa người dùng' : 'Thêm người dùng mới' }}
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="user-form">
            <div class="form-section">
              <h3>Thông tin cá nhân</h3>
              
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Họ và tên *</mat-label>
                  <input matInput formControlName="hoTen" placeholder="Nhập họ và tên">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="userForm.get('hoTen')?.invalid && userForm.get('hoTen')?.touched">
                    {{ getErrorMessage('hoTen') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row two-columns">
                <mat-form-field appearance="outline">
                  <mat-label>Email *</mat-label>
                  <input matInput type="email" formControlName="email" placeholder="Nhập email">
                  <mat-icon matSuffix>email</mat-icon>
                  <mat-error *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched">
                    {{ getErrorMessage('email') }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Số điện thoại</mat-label>
                  <input matInput formControlName="soDienThoai" placeholder="0123456789">
                  <mat-icon matSuffix>phone</mat-icon>
                  <mat-error *ngIf="userForm.get('soDienThoai')?.invalid && userForm.get('soDienThoai')?.touched">
                    {{ getErrorMessage('soDienThoai') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Địa chỉ</mat-label>
                  <input matInput formControlName="diaChi" placeholder="Nhập địa chỉ">
                  <mat-icon matSuffix>location_on</mat-icon>
                  <mat-error *ngIf="userForm.get('diaChi')?.invalid && userForm.get('diaChi')?.touched">
                    {{ getErrorMessage('diaChi') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row two-columns">
                <mat-form-field appearance="outline">
                  <mat-label>Ngày sinh *</mat-label>
                  <input matInput [matDatepicker]="picker" formControlName="ngaySinh" [max]="maxDate">
                  <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker #picker></mat-datepicker>
                  <mat-error *ngIf="userForm.get('ngaySinh')?.invalid && userForm.get('ngaySinh')?.touched">
                    {{ getErrorMessage('ngaySinh') }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Giới tính *</mat-label>
                  <mat-select formControlName="gioiTinh">
                    <mat-option *ngFor="let gender of genderOptions" [value]="gender.value">
                      {{ gender.label }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="userForm.get('gioiTinh')?.invalid && userForm.get('gioiTinh')?.touched">
                    {{ getErrorMessage('gioiTinh') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Số căn cước *</mat-label>
                  <input matInput formControlName="soCanCuoc" placeholder="123456789012">
                  <mat-icon matSuffix>badge</mat-icon>
                  <mat-error *ngIf="userForm.get('soCanCuoc')?.invalid && userForm.get('soCanCuoc')?.touched">
                    {{ getErrorMessage('soCanCuoc') }}
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="form-section">
              <h3>Thông tin hệ thống</h3>
              
              <div class="form-row two-columns">
                <mat-form-field appearance="outline">
                  <mat-label>Vai trò *</mat-label>
                  <mat-select formControlName="vaiTro">
                    <mat-option *ngFor="let role of userRoles" [value]="role.value">
                      {{ role.label }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="userForm.get('vaiTro')?.invalid && userForm.get('vaiTro')?.touched">
                    {{ getErrorMessage('vaiTro') }}
                  </mat-error>
                </mat-form-field>

                <div class="checkbox-field" *ngIf="isEditMode">
                  <mat-checkbox formControlName="trangThaiHoatDong">
                    Tài khoản hoạt động
                  </mat-checkbox>
                </div>
              </div>

              <div class="form-row" *ngIf="!isEditMode">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Mật khẩu</mat-label>
                  <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="matKhau">
                  <button mat-icon-button matSuffix type="button" (click)="togglePasswordVisibility()">
                    <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                  </button>
                  <mat-hint>Để trống để tạo mật khẩu mặc định</mat-hint>
                  <mat-error *ngIf="userForm.get('matKhau')?.invalid && userForm.get('matKhau')?.touched">
                    {{ getErrorMessage('matKhau') }}
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="onCancel()" [disabled]="isLoading">
                <mat-icon>cancel</mat-icon>
                Hủy
              </button>
              <button mat-raised-button color="primary" type="submit" [disabled]="userForm.invalid || isLoading">
                <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
                <mat-icon *ngIf="!isLoading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
                {{ isLoading ? 'Đang xử lý...' : (isEditMode ? 'Cập nhật' : 'Thêm mới') }}
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styleUrls: ['./user-form.component.scss']
})
export class UserFormComponent implements OnInit {
  userForm!: FormGroup;
  isEditMode = false;
  isLoading = false;
  hidePassword = true;
  userId: number | null = null;
  maxDate = new Date();

  userRoles = USER_ROLES;
  genderOptions = GENDER_OPTIONS;

  constructor(
    private formBuilder: FormBuilder,
    private userService: UserService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    // Set max date to 16 years ago (minimum age requirement)
    this.maxDate.setFullYear(this.maxDate.getFullYear() - 16);
  }

  ngOnInit(): void {
    this.initializeForm();
    this.checkEditMode();
  }

  private initializeForm(): void {
    this.userForm = this.formBuilder.group({
      hoTen: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(100)]],
      soDienThoai: ['', [Validators.pattern(/^[0-9]{10,11}$/)]],
      diaChi: ['', [Validators.maxLength(200)]],
      ngaySinh: ['', [Validators.required, this.ageValidator]],
      gioiTinh: ['', [Validators.required]],
      soCanCuoc: ['', [Validators.required, Validators.pattern(/^[0-9]{9,12}$/), Validators.maxLength(20)]],
      vaiTro: ['User', [Validators.required]],
      trangThaiHoatDong: [true],
      matKhau: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  private ageValidator(control: AbstractControl): { [key: string]: any } | null {
    if (!control.value) return null;
    
    const birthDate = new Date(control.value);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age < 16 ? { minAge: { requiredAge: 16, actualAge: age - 1 } } : null;
    }
    
    return age < 16 ? { minAge: { requiredAge: 16, actualAge: age } } : null;
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.userId = parseInt(id, 10);
      this.loadUser();
      // Remove password field for edit mode
      this.userForm.removeControl('matKhau');
    }
  }

  private loadUser(): void {
    if (!this.userId) return;

    this.isLoading = true;
    this.userService.getUserById(this.userId).subscribe({
      next: (user) => {
        this.populateForm(user);
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi tải thông tin người dùng', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/users']);
      }
    });
  }

  private populateForm(user: User): void {
    this.userForm.patchValue({
      hoTen: user.hoTen,
      email: user.email,
      soDienThoai: user.soDienThoai,
      diaChi: user.diaChi,
      ngaySinh: new Date(user.ngaySinh),
      gioiTinh: user.gioiTinh,
      soCanCuoc: user.soCanCuoc,
      vaiTro: user.vaiTro,
      trangThaiHoatDong: user.trangThaiHoatDong
    });
  }

  onSubmit(): void {
    if (this.userForm.valid && !this.isLoading) {
      this.isLoading = true;

      if (this.isEditMode) {
        this.updateUser();
      } else {
        this.createUser();
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private createUser(): void {
    const formValue = this.userForm.value;
    const createRequest: CreateUserRequest = {
      hoTen: formValue.hoTen,
      email: formValue.email,
      soDienThoai: formValue.soDienThoai,
      diaChi: formValue.diaChi,
      ngaySinh: formValue.ngaySinh.toISOString().split('T')[0],
      gioiTinh: formValue.gioiTinh,
      soCanCuoc: formValue.soCanCuoc,
      vaiTro: formValue.vaiTro,
      matKhau: formValue.matKhau
    };

    this.userService.createUser(createRequest).subscribe({
      next: () => {
        this.isLoading = false;
        this.snackBar.open('Thêm người dùng thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/users']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi thêm người dùng', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private updateUser(): void {
    if (!this.userId) return;

    const formValue = this.userForm.value;
    const updateRequest: UpdateUserRequest = {
      hoTen: formValue.hoTen,
      email: formValue.email,
      soDienThoai: formValue.soDienThoai,
      diaChi: formValue.diaChi,
      ngaySinh: formValue.ngaySinh.toISOString().split('T')[0],
      gioiTinh: formValue.gioiTinh,
      soCanCuoc: formValue.soCanCuoc,
      vaiTro: formValue.vaiTro,
      trangThaiHoatDong: formValue.trangThaiHoatDong
    };

    this.userService.updateUser(this.userId, updateRequest).subscribe({
      next: () => {
        this.isLoading = false;
        this.snackBar.open('Cập nhật người dùng thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/users']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi cập nhật người dùng', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/users']);
  }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.userForm.get(fieldName);
    
    if (control?.hasError('required')) {
      return `${this.getFieldDisplayName(fieldName)} là bắt buộc`;
    }
    
    if (control?.hasError('email')) {
      return 'Email không hợp lệ';
    }
    
    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength']?.requiredLength;
      return `${this.getFieldDisplayName(fieldName)} phải có ít nhất ${minLength} ký tự`;
    }
    
    if (control?.hasError('maxlength')) {
      const maxLength = control.errors?.['maxlength']?.requiredLength;
      return `${this.getFieldDisplayName(fieldName)} không được vượt quá ${maxLength} ký tự`;
    }
    
    if (control?.hasError('pattern')) {
      if (fieldName === 'soDienThoai') {
        return 'Số điện thoại phải có 10-11 chữ số';
      }
      if (fieldName === 'soCanCuoc') {
        return 'Số căn cước phải có 9-12 chữ số';
      }
    }
    
    if (control?.hasError('minAge')) {
      const requiredAge = control.errors?.['minAge']?.requiredAge;
      return `Người dùng phải từ ${requiredAge} tuổi trở lên`;
    }
    
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      hoTen: 'Họ tên',
      email: 'Email',
      soDienThoai: 'Số điện thoại',
      diaChi: 'Địa chỉ',
      ngaySinh: 'Ngày sinh',
      gioiTinh: 'Giới tính',
      soCanCuoc: 'Số căn cước',
      vaiTro: 'Vai trò',
      matKhau: 'Mật khẩu'
    };
    return fieldNames[fieldName] || fieldName;
  }
}
