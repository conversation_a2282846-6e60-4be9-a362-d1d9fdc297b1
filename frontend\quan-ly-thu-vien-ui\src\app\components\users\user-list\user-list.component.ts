import { <PERSON>mpo<PERSON>, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSortModule, MatSort, Sort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { Mat<PERSON>nackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { UserService } from '../../../services/user.service';
import { AuthService } from '../../../services/auth.service';
import { User, UserSearchRequest, USER_ROLES } from '../../../models/user.model';

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatTooltipModule
  ],
  template: `
    <div class="user-list-container">
      <mat-card class="search-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>people</mat-icon>
            Quản lý người dùng
          </mat-card-title>
          <div class="header-actions">
            <button mat-raised-button color="primary" routerLink="/users/add" *ngIf="canManageUsers()">
              <mat-icon>person_add</mat-icon>
              Thêm người dùng
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="searchForm" class="search-form">
            <div class="search-row">
              <mat-form-field appearance="outline">
                <mat-label>Họ tên</mat-label>
                <input matInput formControlName="hoTen" placeholder="Tìm theo họ tên">
                <mat-icon matSuffix>person</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Email</mat-label>
                <input matInput formControlName="email" placeholder="Tìm theo email">
                <mat-icon matSuffix>email</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Vai trò</mat-label>
                <mat-select formControlName="vaiTro">
                  <mat-option value="">Tất cả</mat-option>
                  <mat-option *ngFor="let role of userRoles" [value]="role.value">
                    {{ role.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Trạng thái</mat-label>
                <mat-select formControlName="trangThaiHoatDong">
                  <mat-option value="">Tất cả</mat-option>
                  <mat-option [value]="true">Hoạt động</mat-option>
                  <mat-option [value]="false">Không hoạt động</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="search-actions">
              <button mat-raised-button color="primary" (click)="onSearch()" [disabled]="isLoading">
                <mat-icon>search</mat-icon>
                Tìm kiếm
              </button>
              <button mat-button (click)="onReset()" [disabled]="isLoading">
                <mat-icon>refresh</mat-icon>
                Đặt lại
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <mat-card class="table-card">
        <mat-card-content>
          <div class="table-container">
            <mat-table [dataSource]="dataSource" matSort class="user-table">
              <ng-container matColumnDef="hoTen">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Họ tên</mat-header-cell>
                <mat-cell *matCellDef="let user">
                  <div class="user-info">
                    <span class="user-name">{{ user.hoTen }}</span>
                    <span class="user-email">{{ user.email }}</span>
                  </div>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="soDienThoai">
                <mat-header-cell *matHeaderCellDef>Số điện thoại</mat-header-cell>
                <mat-cell *matCellDef="let user">{{ user.soDienThoai || 'N/A' }}</mat-cell>
              </ng-container>

              <ng-container matColumnDef="vaiTro">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Vai trò</mat-header-cell>
                <mat-cell *matCellDef="let user">
                  <mat-chip [ngClass]="getRoleClass(user.vaiTro)">
                    {{ getRoleDisplayName(user.vaiTro) }}
                  </mat-chip>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="trangThaiHoatDong">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Trạng thái</mat-header-cell>
                <mat-cell *matCellDef="let user">
                  <mat-chip [ngClass]="user.trangThaiHoatDong ? 'status-active' : 'status-inactive'">
                    {{ user.trangThaiHoatDong ? 'Hoạt động' : 'Không hoạt động' }}
                  </mat-chip>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="ngayDangKy">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Ngày đăng ký</mat-header-cell>
                <mat-cell *matCellDef="let user">{{ formatDate(user.ngayDangKy) }}</mat-cell>
              </ng-container>

              <ng-container matColumnDef="actions">
                <mat-header-cell *matHeaderCellDef>Thao tác</mat-header-cell>
                <mat-cell *matCellDef="let user">
                  <div class="action-buttons">
                    <button mat-icon-button color="primary" 
                            [routerLink]="['/users/edit', user.id]"
                            matTooltip="Chỉnh sửa"
                            *ngIf="canManageUsers()">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" 
                            (click)="onDelete(user)"
                            matTooltip="Xóa"
                            *ngIf="canManageUsers() && user.id !== currentUserId">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </mat-cell>
              </ng-container>

              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </mat-table>

            <div class="loading-container" *ngIf="isLoading">
              <mat-spinner diameter="50"></mat-spinner>
            </div>

            <div class="no-data" *ngIf="!isLoading && dataSource.data.length === 0">
              <mat-icon>people_outline</mat-icon>
              <p>Không tìm thấy người dùng nào</p>
            </div>
          </div>

          <mat-paginator #paginator
                         [length]="totalCount"
                         [pageSize]="pageSize"
                         [pageSizeOptions]="[5, 10, 25, 50]"
                         [showFirstLastButtons]="true"
                         (page)="onPageChange($event)">
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  searchForm!: FormGroup;
  dataSource = new MatTableDataSource<User>([]);
  displayedColumns: string[] = ['hoTen', 'soDienThoai', 'vaiTro', 'trangThaiHoatDong', 'ngayDangKy', 'actions'];
  
  isLoading = false;
  totalCount = 0;
  pageSize = 10;
  currentPage = 1;
  currentUserId: number | null = null;
  
  userRoles = USER_ROLES;

  constructor(
    private userService: UserService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.getCurrentUser();
    this.loadUsers();
  }

  private initializeForm(): void {
    this.searchForm = this.formBuilder.group({
      hoTen: [''],
      email: [''],
      vaiTro: [''],
      trangThaiHoatDong: ['']
    });
  }

  private getCurrentUser(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUserId = user?.id || null;
    });
  }

  loadUsers(): void {
    this.isLoading = true;
    
    const searchRequest: UserSearchRequest = {
      ...this.searchForm.value,
      page: this.currentPage,
      pageSize: this.pageSize,
      sortBy: this.sort?.active || 'hoTen',
      sortDescending: this.sort?.direction === 'desc'
    };

    this.userService.getUsers(searchRequest).subscribe({
      next: (result) => {
        this.dataSource.data = result.items;
        this.totalCount = result.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi tải danh sách người dùng', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSearch(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onReset(): void {
    this.searchForm.reset();
    this.currentPage = 1;
    this.loadUsers();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadUsers();
  }

  onSortChange(sort: Sort): void {
    this.loadUsers();
  }

  onDelete(user: User): void {
    if (confirm(`Bạn có chắc chắn muốn xóa người dùng "${user.hoTen}"?`)) {
      this.userService.deleteUser(user.id).subscribe({
        next: () => {
          this.snackBar.open('Xóa người dùng thành công', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadUsers();
        },
        error: (error) => {
          this.snackBar.open(error.message || 'Lỗi khi xóa người dùng', 'Đóng', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  canManageUsers(): boolean {
    return this.authService.canManageUsers();
  }

  getRoleDisplayName(role: string): string {
    const roleObj = this.userRoles.find(r => r.value === role);
    return roleObj?.label || role;
  }

  getRoleClass(role: string): string {
    return `role-${role.toLowerCase()}`;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('vi-VN');
  }
}
