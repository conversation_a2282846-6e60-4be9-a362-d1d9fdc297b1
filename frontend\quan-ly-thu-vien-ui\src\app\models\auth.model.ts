export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  hoTen: string;
  email: string;
  password: string;
  confirmPassword: string;
  soDienThoai?: string;
  diaChi?: string;
  ngaySinh: string;
  gioiTinh: string;
  soCanCuoc: string;
}

export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiration: string;
  refreshTokenExpiration: string;
  tokenType: string;
  user: UserInfo;
}

export interface UserInfo {
  id: number;
  hoTen: string;
  email: string;
  vaiTro: string;
  trangThaiHoatDong: boolean;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  success?: boolean;
}
