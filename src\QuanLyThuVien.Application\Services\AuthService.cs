using Microsoft.Extensions.Options;
using QuanLyThuVien.Application.Configuration;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Services;

public class AuthService : IAuthService
{
    private readonly INguoiDungRepository _nguoiDungRepository;
    private readonly IRefreshTokenRepository _refreshTokenRepository;
    private readonly IJwtService _jwtService;
    private readonly JwtSettings _jwtSettings;

    public AuthService(
        INguoiDungRepository nguoiDungRepository,
        IRefreshTokenRepository refreshTokenRepository,
        IJwtService jwtService,
        IOptions<JwtSettings> jwtSettings)
    {
        _nguoiDungRepository = nguoiDungRepository;
        _refreshTokenRepository = refreshTokenRepository;
        _jwtService = jwtService;
        _jwtSettings = jwtSettings.Value;
    }

    public async Task<TokenResponseDto> LoginAsync(LoginDto loginDto, string ipAddress)
    {
        // Validate user credentials
        var nguoiDung = await _nguoiDungRepository.GetByEmailAsync(loginDto.Email);
        if (nguoiDung == null)
        {
            throw new UnauthorizedAccessException("Email hoặc mật khẩu không chính xác.");
        }

        if (!nguoiDung.TrangThaiHoatDong)
        {
            throw new UnauthorizedAccessException("Tài khoản đã bị vô hiệu hóa.");
        }

        if (string.IsNullOrEmpty(nguoiDung.MatKhauHash) || 
            !BCrypt.Net.BCrypt.Verify(loginDto.Password, nguoiDung.MatKhauHash))
        {
            throw new UnauthorizedAccessException("Email hoặc mật khẩu không chính xác.");
        }

        // Generate tokens
        var accessToken = _jwtService.GenerateAccessToken(nguoiDung);
        var refreshToken = await GenerateRefreshTokenAsync(nguoiDung.Id, ipAddress);

        return new TokenResponseDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken.Token,
            AccessTokenExpiration = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes),
            RefreshTokenExpiration = refreshToken.ExpiryDate,
            User = MapToUserInfo(nguoiDung)
        };
    }

    public async Task<TokenResponseDto> RegisterAsync(RegisterDto registerDto, string ipAddress)
    {
        // Check if email already exists
        if (await _nguoiDungRepository.IsEmailExistsAsync(registerDto.Email))
        {
            throw new InvalidOperationException($"Email '{registerDto.Email}' đã tồn tại trong hệ thống.");
        }

        // Check if SoCanCuoc already exists
        if (await _nguoiDungRepository.IsSoCanCuocExistsAsync(registerDto.SoCanCuoc))
        {
            throw new InvalidOperationException($"Số căn cước '{registerDto.SoCanCuoc}' đã tồn tại trong hệ thống.");
        }

        // Validate age (must be at least 16 years old)
        var age = DateTime.Now.Year - registerDto.NgaySinh.Year;
        if (registerDto.NgaySinh.Date > DateTime.Now.AddYears(-age)) age--;
        
        if (age < 16)
        {
            throw new InvalidOperationException("Người dùng phải từ 16 tuổi trở lên.");
        }

        // Create new user
        var nguoiDung = new NguoiDung
        {
            HoTen = registerDto.HoTen,
            Email = registerDto.Email,
            SoDienThoai = registerDto.SoDienThoai,
            DiaChi = registerDto.DiaChi,
            NgaySinh = registerDto.NgaySinh,
            GioiTinh = registerDto.GioiTinh,
            SoCanCuoc = registerDto.SoCanCuoc,
            VaiTro = "User", // Default role
            TrangThaiHoatDong = true,
            MatKhauHash = BCrypt.Net.BCrypt.HashPassword(registerDto.Password)
        };

        var createdNguoiDung = await _nguoiDungRepository.CreateAsync(nguoiDung);

        // Generate tokens
        var accessToken = _jwtService.GenerateAccessToken(createdNguoiDung);
        var refreshToken = await GenerateRefreshTokenAsync(createdNguoiDung.Id, ipAddress);

        return new TokenResponseDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken.Token,
            AccessTokenExpiration = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes),
            RefreshTokenExpiration = refreshToken.ExpiryDate,
            User = MapToUserInfo(createdNguoiDung)
        };
    }

    public async Task<TokenResponseDto> RefreshTokenAsync(string refreshToken, string ipAddress)
    {
        var token = await _refreshTokenRepository.GetByTokenAsync(refreshToken);
        
        if (token == null || !token.IsActive)
        {
            throw new UnauthorizedAccessException("Refresh token không hợp lệ hoặc đã hết hạn.");
        }

        // Revoke current token and create new one
        token.IsRevoked = true;
        token.RevokedDate = DateTime.UtcNow;
        token.RevokedByIp = ipAddress;

        var newRefreshToken = await GenerateRefreshTokenAsync(token.NguoiDungId, ipAddress);
        token.ReplacedByToken = newRefreshToken.Token;

        await _refreshTokenRepository.UpdateAsync(token);

        // Generate new access token
        var nguoiDung = await _nguoiDungRepository.GetByIdAsync(token.NguoiDungId);
        if (nguoiDung == null || !nguoiDung.TrangThaiHoatDong)
        {
            throw new UnauthorizedAccessException("Người dùng không tồn tại hoặc đã bị vô hiệu hóa.");
        }

        var accessToken = _jwtService.GenerateAccessToken(nguoiDung);

        return new TokenResponseDto
        {
            AccessToken = accessToken,
            RefreshToken = newRefreshToken.Token,
            AccessTokenExpiration = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes),
            RefreshTokenExpiration = newRefreshToken.ExpiryDate,
            User = MapToUserInfo(nguoiDung)
        };
    }

    public async Task<bool> RevokeTokenAsync(string refreshToken, string ipAddress)
    {
        return await _refreshTokenRepository.RevokeTokenAsync(refreshToken, ipAddress);
    }

    public async Task<bool> RevokeAllTokensAsync(int userId, string ipAddress)
    {
        return await _refreshTokenRepository.RevokeAllUserTokensAsync(userId, ipAddress);
    }

    public async Task<bool> ChangePasswordAsync(int userId, ChangePasswordRequestDto changePasswordDto)
    {
        var nguoiDung = await _nguoiDungRepository.GetByIdAsync(userId);
        if (nguoiDung == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy người dùng với ID: {userId}");
        }

        // Verify current password
        if (string.IsNullOrEmpty(nguoiDung.MatKhauHash) || 
            !BCrypt.Net.BCrypt.Verify(changePasswordDto.CurrentPassword, nguoiDung.MatKhauHash))
        {
            throw new InvalidOperationException("Mật khẩu hiện tại không chính xác.");
        }

        // Update password
        nguoiDung.MatKhauHash = BCrypt.Net.BCrypt.HashPassword(changePasswordDto.NewPassword);
        await _nguoiDungRepository.UpdateAsync(nguoiDung);

        return true;
    }

    public async Task<UserInfoDto?> GetCurrentUserAsync(int userId)
    {
        var nguoiDung = await _nguoiDungRepository.GetByIdAsync(userId);
        return nguoiDung != null ? MapToUserInfo(nguoiDung) : null;
    }

    public async Task<bool> ValidateRefreshTokenAsync(string refreshToken)
    {
        var token = await _refreshTokenRepository.GetByTokenAsync(refreshToken);
        return token != null && token.IsActive;
    }

    public async Task<bool> LogoutAsync(string refreshToken, string ipAddress)
    {
        return await _refreshTokenRepository.RevokeTokenAsync(refreshToken, ipAddress);
    }

    private async Task<RefreshToken> GenerateRefreshTokenAsync(int userId, string ipAddress)
    {
        var refreshToken = new RefreshToken
        {
            Token = _jwtService.GenerateRefreshToken(),
            NguoiDungId = userId,
            ExpiryDate = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpirationDays),
            CreatedByIp = ipAddress
        };

        return await _refreshTokenRepository.CreateAsync(refreshToken);
    }

    private static UserInfoDto MapToUserInfo(NguoiDung nguoiDung)
    {
        return new UserInfoDto
        {
            Id = nguoiDung.Id,
            HoTen = nguoiDung.HoTen,
            Email = nguoiDung.Email,
            VaiTro = nguoiDung.VaiTro,
            TrangThaiHoatDong = nguoiDung.TrangThaiHoatDong
        };
    }
}
