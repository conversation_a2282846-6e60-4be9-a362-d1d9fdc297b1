import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { BorrowingService } from '../../../services/borrowing.service';
import { Borrowing, ReturnBookRequest } from '../../../models/borrowing.model';

@Component({
  selector: 'app-return-book-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <div class="return-dialog">
      <h2 mat-dialog-title>
        <mat-icon>assignment_return</mat-icon>
        Trả sách
      </h2>

      <mat-dialog-content>
        <div class="borrowing-info">
          <h3>Thông tin mượn sách</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>Người mượn:</label>
              <span>{{ borrowing.nguoiDung?.hoTen }}</span>
            </div>
            <div class="info-item">
              <label>Email:</label>
              <span>{{ borrowing.nguoiDung?.email }}</span>
            </div>
            <div class="info-item">
              <label>Sách:</label>
              <span>{{ borrowing.sach?.tenSach }}</span>
            </div>
            <div class="info-item">
              <label>Tác giả:</label>
              <span>{{ borrowing.sach?.tacGia }}</span>
            </div>
            <div class="info-item">
              <label>Ngày mượn:</label>
              <span>{{ formatDate(borrowing.ngayMuon) }}</span>
            </div>
            <div class="info-item">
              <label>Ngày hẹn trả:</label>
              <span [ngClass]="getDueDateClass()">{{ formatDate(borrowing.ngayHenTra) }}</span>
            </div>
            <div class="info-item">
              <label>Số ngày mượn:</label>
              <span>{{ getBorrowingDays() }} ngày</span>
            </div>
            <div class="info-item" *ngIf="isOverdue()">
              <label>Số ngày quá hạn:</label>
              <span class="overdue-days">{{ getOverdueDays() }} ngày</span>
            </div>
          </div>
        </div>

        <form [formGroup]="returnForm" class="return-form">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Ngày trả thực tế *</mat-label>
            <input matInput [matDatepicker]="returnPicker" formControlName="ngayTraThucTe" [max]="maxReturnDate">
            <mat-datepicker-toggle matSuffix [for]="returnPicker"></mat-datepicker-toggle>
            <mat-datepicker #returnPicker></mat-datepicker>
            <mat-error *ngIf="returnForm.get('ngayTraThucTe')?.invalid && returnForm.get('ngayTraThucTe')?.touched">
              Ngày trả thực tế là bắt buộc
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Ghi chú</mat-label>
            <textarea matInput formControlName="ghiChu" rows="3" 
                      placeholder="Nhập ghi chú về tình trạng sách khi trả (nếu có)..."></textarea>
            <mat-icon matSuffix>notes</mat-icon>
          </mat-form-field>

          <div class="return-summary" *ngIf="returnForm.get('ngayTraThucTe')?.value">
            <h4>Tóm tắt trả sách</h4>
            <div class="summary-grid">
              <div class="summary-item">
                <label>Thời gian mượn thực tế:</label>
                <span>{{ getActualBorrowingDays() }} ngày</span>
              </div>
              <div class="summary-item" *ngIf="isLateReturn()">
                <label>Trả muộn:</label>
                <span class="late-return">{{ getLateDays() }} ngày</span>
              </div>
              <div class="summary-item" *ngIf="isEarlyReturn()">
                <label>Trả sớm:</label>
                <span class="early-return">{{ getEarlyDays() }} ngày</span>
              </div>
            </div>
          </div>
        </form>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button mat-button (click)="onCancel()" [disabled]="isLoading">
          <mat-icon>cancel</mat-icon>
          Hủy
        </button>
        <button mat-raised-button color="primary" (click)="onReturn()" 
                [disabled]="returnForm.invalid || isLoading">
          <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
          <mat-icon *ngIf="!isLoading">assignment_return</mat-icon>
          {{ isLoading ? 'Đang xử lý...' : 'Trả sách' }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .return-dialog {
      width: 600px;
      max-width: 90vw;
    }

    h2[mat-dialog-title] {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 16px 0;
      color: #333;
    }

    .borrowing-info {
      background-color: #f5f5f5;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 20px;

      h3 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      label {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }

      span {
        color: #333;
        font-weight: 400;
      }

      .overdue-days {
        color: #f44336;
        font-weight: 600;
      }
    }

    .due-date-overdue {
      color: #f44336;
      font-weight: 600;
    }

    .due-date-warning {
      color: #ff9800;
      font-weight: 500;
    }

    .due-date-normal {
      color: #333;
    }

    .return-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .full-width {
      width: 100%;
    }

    .return-summary {
      background-color: #e8f5e8;
      padding: 16px;
      border-radius: 8px;
      border-left: 4px solid #4caf50;

      h4 {
        margin: 0 0 12px 0;
        color: #2e7d32;
        font-size: 14px;
      }
    }

    .summary-grid {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      label {
        font-size: 13px;
        color: #555;
      }

      span {
        font-weight: 500;
        
        &.late-return {
          color: #f44336;
        }
        
        &.early-return {
          color: #4caf50;
        }
      }
    }

    mat-dialog-actions {
      margin-top: 20px;
      padding: 16px 0 0 0;
      border-top: 1px solid #e0e0e0;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 100px;
      }
    }

    .button-spinner {
      margin-right: 8px;
    }

    @media (max-width: 768px) {
      .return-dialog {
        width: 100%;
        max-width: 100vw;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      mat-dialog-actions {
        flex-direction: column-reverse;
        gap: 8px;
        
        button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  `]
})
export class ReturnBookDialogComponent implements OnInit {
  returnForm!: FormGroup;
  isLoading = false;
  maxReturnDate = new Date();

  constructor(
    public dialogRef: MatDialogRef<ReturnBookDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public borrowing: Borrowing,
    private formBuilder: FormBuilder,
    private borrowingService: BorrowingService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.returnForm = this.formBuilder.group({
      ngayTraThucTe: [new Date(), [Validators.required]],
      ghiChu: ['']
    });
  }

  onReturn(): void {
    if (this.returnForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      const returnData: ReturnBookRequest = {
        ngayTraThucTe: this.returnForm.value.ngayTraThucTe.toISOString().split('T')[0],
        ghiChu: this.returnForm.value.ghiChu
      };

      this.borrowingService.returnBook(this.borrowing.id, returnData).subscribe({
        next: (updatedBorrowing) => {
          this.isLoading = false;
          this.snackBar.open('Trả sách thành công!', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.dialogRef.close(updatedBorrowing);
        },
        error: (error) => {
          this.isLoading = false;
          this.snackBar.open(error.message || 'Lỗi khi trả sách', 'Đóng', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('vi-VN');
  }

  getBorrowingDays(): number {
    const borrowDate = new Date(this.borrowing.ngayMuon);
    const dueDate = new Date(this.borrowing.ngayHenTra);
    return Math.ceil((dueDate.getTime() - borrowDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  getActualBorrowingDays(): number {
    const borrowDate = new Date(this.borrowing.ngayMuon);
    const returnDate = this.returnForm.value.ngayTraThucTe;
    if (!returnDate) return 0;
    return Math.ceil((returnDate.getTime() - borrowDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  isOverdue(): boolean {
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const today = new Date();
    return today > dueDate;
  }

  getOverdueDays(): number {
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const today = new Date();
    return Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  isLateReturn(): boolean {
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const returnDate = this.returnForm.value.ngayTraThucTe;
    return returnDate && returnDate > dueDate;
  }

  isEarlyReturn(): boolean {
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const returnDate = this.returnForm.value.ngayTraThucTe;
    return returnDate && returnDate < dueDate;
  }

  getLateDays(): number {
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const returnDate = this.returnForm.value.ngayTraThucTe;
    if (!returnDate || returnDate <= dueDate) return 0;
    return Math.ceil((returnDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  getEarlyDays(): number {
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const returnDate = this.returnForm.value.ngayTraThucTe;
    if (!returnDate || returnDate >= dueDate) return 0;
    return Math.ceil((dueDate.getTime() - returnDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  getDueDateClass(): string {
    if (this.isOverdue()) return 'due-date-overdue';
    
    const dueDate = new Date(this.borrowing.ngayHenTra);
    const today = new Date();
    const diffDays = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 3) return 'due-date-warning';
    return 'due-date-normal';
  }
}
