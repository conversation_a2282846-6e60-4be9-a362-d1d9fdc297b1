using Microsoft.AspNetCore.Mvc;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Domain.Enums;

namespace QuanLyThuVien.WebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SeedController : ControllerBase
    {
        private readonly ISachService _sachService;
        private readonly INguoiDungService _nguoiDungService;

        public SeedController(ISachService sachService, INguoiDungService nguoiDungService)
        {
            _sachService = sachService;
            _nguoiDungService = nguoiDungService;
        }

        [HttpPost("sample-data")]
        public async Task<IActionResult> SeedSampleData()
        {
            try
            {
                // Seed sample books
                var sampleBooks = new List<CreateSachDto>
                {
                    new CreateSachDto
                    {
                        TenSach = "Clean Code: A Handbook of Agile Software Craftsmanship",
                        TacGia = "<PERSON>",
                        NhaXuatBan = "NXB Khoa học và <PERSON>ỹ thuật",
                        NamXuatBan = 2008,
                        ISBN = "978-0132350884",
                        SoTrang = 464,
                        TheLoai = "Công nghệ thông tin",
                        SoLuongTong = 10,
                        MoTa = "Sách hướng dẫn viết code sạch và dễ bảo trì",
                        Gia = 450000
                    },
                    new CreateSachDto
                    {
                        TenSach = "Design Patterns: Elements of Reusable Object-Oriented Software",
                        TacGia = "Gang of Four",
                        NhaXuatBan = "NXB Khoa học và Kỹ thuật",
                        NamXuatBan = 1994,
                        ISBN = "978-0201633610",
                        SoTrang = 395,
                        TheLoai = "Công nghệ thông tin",
                        SoLuongTong = 8,
                        MoTa = "Sách về các mẫu thiết kế trong lập trình hướng đối tượng",
                        Gia = 520000
                    },
                    new CreateSachDto
                    {
                        TenSach = "JavaScript: The Good Parts",
                        TacGia = "Douglas Crockford",
                        NhaXuatBan = "NXB Thông tin và Truyền thông",
                        NamXuatBan = 2008,
                        ISBN = "978-0596517748",
                        SoTrang = 176,
                        TheLoai = "Công nghệ thông tin",
                        SoLuongTong = 12,
                        MoTa = "Sách về những phần tốt nhất của JavaScript",
                        Gia = 380000
                    },
                    new CreateSachDto
                    {
                        TenSach = "Truyện Kiều",
                        TacGia = "Nguyễn Du",
                        NhaXuatBan = "NXB Văn học",
                        NamXuatBan = 2020,
                        ISBN = "978-6041234567",
                        SoTrang = 320,
                        TheLoai = "Văn học",
                        SoLuongTong = 15,
                        MoTa = "Tác phẩm văn học kinh điển của Việt Nam",
                        Gia = 150000
                    },
                    new CreateSachDto
                    {
                        TenSach = "Lịch sử Việt Nam",
                        TacGia = "Trần Trọng Kim",
                        NhaXuatBan = "NXB Hà Nội",
                        NamXuatBan = 2019,
                        ISBN = "978-6041567890",
                        SoTrang = 580,
                        TheLoai = "Lịch sử",
                        SoLuongTong = 6,
                        MoTa = "Tổng quan về lịch sử Việt Nam qua các thời kỳ",
                        Gia = 280000
                    }
                };

                foreach (var book in sampleBooks)
                {
                    await _sachService.CreateAsync(book);
                }

                // Seed sample users
                var sampleUsers = new List<CreateNguoiDungDto>
                {
                    new CreateNguoiDungDto
                    {
                        HoTen = "Nguyễn Văn A",
                        Email = "<EMAIL>",
                        SoDienThoai = "0123456789",
                        DiaChi = "123 Đường ABC, Quận 1, TP.HCM",
                        NgaySinh = new DateTime(1990, 5, 15),
                        GioiTinh = "Nam",
                        SoCanCuoc = "123456789012",
                        VaiTro = VaiTro.User,
                        MatKhau = "user123"
                    },
                    new CreateNguoiDungDto
                    {
                        HoTen = "Trần Thị B",
                        Email = "<EMAIL>",
                        SoDienThoai = "0987654321",
                        DiaChi = "456 Đường XYZ, Quận 2, TP.HCM",
                        NgaySinh = new DateTime(1985, 8, 20),
                        GioiTinh = "Nữ",
                        SoCanCuoc = "987654321098",
                        VaiTro = VaiTro.User,
                        MatKhau = "user123"
                    },
                    new CreateNguoiDungDto
                    {
                        HoTen = "Lê Văn C",
                        Email = "<EMAIL>",
                        SoDienThoai = "0555666777",
                        DiaChi = "789 Đường DEF, Quận 3, TP.HCM",
                        NgaySinh = new DateTime(1992, 12, 10),
                        GioiTinh = "Nam",
                        SoCanCuoc = "555666777888",
                        VaiTro = VaiTro.User,
                        MatKhau = "user123"
                    }
                };

                foreach (var user in sampleUsers)
                {
                    await _nguoiDungService.CreateAsync(user);
                }

                return Ok(new { 
                    message = "Sample data seeded successfully",
                    booksCreated = sampleBooks.Count,
                    usersCreated = sampleUsers.Count
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "Error seeding data", error = ex.Message });
            }
        }

        [HttpGet("check-data")]
        public async Task<IActionResult> CheckData()
        {
            try
            {
                var searchDto = new SachSearchDto
                {
                    Page = 1,
                    PageSize = 10,
                    SortBy = "TenSach",
                    SortDescending = false
                };

                var books = await _sachService.GetAllAsync(searchDto);
                var userSearchDto = new NguoiDungSearchDto
                {
                    Page = 1,
                    PageSize = 10,
                    SortBy = "HoTen",
                    SortDescending = false
                };
                var users = await _nguoiDungService.GetAllAsync(userSearchDto);

                return Ok(new { 
                    totalBooks = books.TotalCount,
                    totalUsers = users.TotalCount,
                    books = books.Items.Take(3),
                    users = users.Items.Take(3)
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "Error checking data", error = ex.Message });
            }
        }
    }
}
