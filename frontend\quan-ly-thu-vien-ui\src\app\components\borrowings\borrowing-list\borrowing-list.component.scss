.borrowing-list-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-card {
  .mat-mdc-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
  }

  .mat-mdc-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.search-form {
  .search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }

  .checkbox-field {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 0 12px;
  }

  .search-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}

.table-card {
  flex: 1;
}

.table-container {
  position: relative;
  min-height: 400px;
}

.borrowing-table {
  width: 100%;

  .mat-mdc-header-cell {
    font-weight: 600;
    color: #333;
  }

  .mat-mdc-cell {
    padding: 12px 8px;
  }
}

.user-info, .book-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .user-name, .book-title {
    font-weight: 500;
    color: #333;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .user-email, .book-author {
    font-size: 12px;
    color: #666;
  }
}

.action-buttons {
  display: flex;
  gap: 4px;
}

// Status chip styles
.mat-mdc-chip {
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.status-primary {
    background-color: #2196f3;
    color: white;
  }

  &.status-success {
    background-color: #4caf50;
    color: white;
  }

  &.status-warn {
    background-color: #ff9800;
    color: white;
  }

  &.status-danger {
    background-color: #f44336;
    color: white;
  }
}

// Due date styles
.due-date-normal {
  color: #333;
}

.due-date-warning {
  color: #ff9800;
  font-weight: 500;
}

.due-date-overdue {
  color: #f44336;
  font-weight: 600;
}

.due-date-returned {
  color: #4caf50;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .borrowing-list-container {
    padding: 16px;
  }

  .search-card .mat-mdc-card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    
    button {
      width: 100%;
    }
  }

  .search-form .search-row {
    grid-template-columns: 1fr;
  }

  .search-actions {
    justify-content: stretch;
    
    button {
      flex: 1;
    }
  }

  .borrowing-table {
    .mat-mdc-cell,
    .mat-mdc-header-cell {
      padding: 8px 4px;
      font-size: 12px;
    }
  }

  .user-info .user-name,
  .book-info .book-title {
    max-width: 120px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
}

@media (max-width: 480px) {
  .borrowing-list-container {
    padding: 12px;
  }

  // Hide some columns on mobile
  .borrowing-table {
    .mat-column-ngayTraThucTe {
      display: none;
    }
  }
}

// Custom snackbar styles
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

::ng-deep .info-snackbar {
  background-color: #2196f3 !important;
  color: white !important;
}

// Table hover effects
.borrowing-table .mat-mdc-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

// Button hover effects
.action-buttons button:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

// Disabled button styles
.action-buttons button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

// Search form enhancements
.search-form {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      border-radius: 8px;
    }
  }
}

// Card enhancements
.search-card,
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// Animation for loading
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.borrowing-table .mat-mdc-row {
  animation: fadeIn 0.3s ease-in;
}

// Overdue highlighting
.borrowing-table .mat-mdc-row {
  &.overdue-row {
    background-color: rgba(244, 67, 54, 0.05);
    border-left: 4px solid #f44336;
  }
}
