import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Observable, startWith, map } from 'rxjs';

import { BorrowingService } from '../../../services/borrowing.service';
import { UserService } from '../../../services/user.service';
import { BookService } from '../../../services/book.service';
import { AuthService } from '../../../services/auth.service';
import { 
  Borrowing, 
  CreateBorrowingRequest, 
  UpdateBorrowingRequest, 
  DEFAULT_BORROWING_DAYS 
} from '../../../models/borrowing.model';
import { User } from '../../../models/user.model';
import { Book } from '../../../models/book.model';

@Component({
  selector: 'app-borrowing-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatAutocompleteModule
  ],
  template: `
    <div class="borrowing-form-container">
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>{{ isEditMode ? 'edit' : 'assignment_add' }}</mat-icon>
            {{ isEditMode ? 'Chỉnh sửa mượn sách' : 'Mượn sách mới' }}
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="borrowingForm" (ngSubmit)="onSubmit()" class="borrowing-form">
            <div class="form-section">
              <h3>Thông tin mượn sách</h3>
              
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Người mượn *</mat-label>
                  <input matInput 
                         formControlName="nguoiDungSearch"
                         placeholder="Tìm người dùng..."
                         [matAutocomplete]="userAuto">
                  <mat-icon matSuffix>person_search</mat-icon>
                  <mat-autocomplete #userAuto="matAutocomplete" [displayWith]="displayUser">
                    <mat-option *ngFor="let user of filteredUsers | async" 
                                [value]="user"
                                (onSelectionChange)="onUserSelected(user)">
                      <div class="user-option">
                        <span class="user-name">{{ user.hoTen }}</span>
                        <span class="user-email">{{ user.email }}</span>
                      </div>
                    </mat-option>
                  </mat-autocomplete>
                  <mat-error *ngIf="borrowingForm.get('nguoiDungId')?.invalid && borrowingForm.get('nguoiDungId')?.touched">
                    Vui lòng chọn người mượn
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Sách *</mat-label>
                  <input matInput 
                         formControlName="sachSearch"
                         placeholder="Tìm sách..."
                         [matAutocomplete]="bookAuto">
                  <mat-icon matSuffix>book_search</mat-icon>
                  <mat-autocomplete #bookAuto="matAutocomplete" [displayWith]="displayBook">
                    <mat-option *ngFor="let book of filteredBooks | async" 
                                [value]="book"
                                (onSelectionChange)="onBookSelected(book)"
                                [disabled]="book.soLuongConLai === 0">
                      <div class="book-option">
                        <span class="book-title">{{ book.tenSach }}</span>
                        <span class="book-author">{{ book.tacGia }}</span>
                        <span class="book-availability" 
                              [ngClass]="book.soLuongConLai === 0 ? 'unavailable' : 'available'">
                          Còn lại: {{ book.soLuongConLai }}
                        </span>
                      </div>
                    </mat-option>
                  </mat-autocomplete>
                  <mat-error *ngIf="borrowingForm.get('sachId')?.invalid && borrowingForm.get('sachId')?.touched">
                    Vui lòng chọn sách
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row two-columns">
                <mat-form-field appearance="outline">
                  <mat-label>Ngày mượn</mat-label>
                  <input matInput [matDatepicker]="borrowPicker" formControlName="ngayMuon" readonly>
                  <mat-datepicker-toggle matSuffix [for]="borrowPicker"></mat-datepicker-toggle>
                  <mat-datepicker #borrowPicker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Ngày hẹn trả *</mat-label>
                  <input matInput [matDatepicker]="duePicker" formControlName="ngayHenTra" [min]="minDueDate">
                  <mat-datepicker-toggle matSuffix [for]="duePicker"></mat-datepicker-toggle>
                  <mat-datepicker #duePicker></mat-datepicker>
                  <mat-error *ngIf="borrowingForm.get('ngayHenTra')?.invalid && borrowingForm.get('ngayHenTra')?.touched">
                    Ngày hẹn trả là bắt buộc
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Ghi chú</mat-label>
                  <textarea matInput formControlName="ghiChu" rows="3" placeholder="Nhập ghi chú (nếu có)..."></textarea>
                  <mat-icon matSuffix>notes</mat-icon>
                </mat-form-field>
              </div>

              <div class="borrowing-info" *ngIf="selectedUser && selectedBook">
                <h4>Thông tin mượn sách</h4>
                <div class="info-grid">
                  <div class="info-item">
                    <label>Người mượn:</label>
                    <span>{{ selectedUser.hoTen }} ({{ selectedUser.email }})</span>
                  </div>
                  <div class="info-item">
                    <label>Sách:</label>
                    <span>{{ selectedBook.tenSach }} - {{ selectedBook.tacGia }}</span>
                  </div>
                  <div class="info-item">
                    <label>Số lượng còn lại:</label>
                    <span [ngClass]="selectedBook.soLuongConLai === 0 ? 'unavailable' : 'available'">
                      {{ selectedBook.soLuongConLai }}/{{ selectedBook.soLuongTong }}
                    </span>
                  </div>
                  <div class="info-item">
                    <label>Thời gian mượn:</label>
                    <span>{{ getBorrowingDuration() }} ngày</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="onCancel()" [disabled]="isLoading">
                <mat-icon>cancel</mat-icon>
                Hủy
              </button>
              <button mat-raised-button color="primary" type="submit" 
                      [disabled]="borrowingForm.invalid || isLoading || (selectedBook && selectedBook.soLuongConLai === 0)">
                <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
                <mat-icon *ngIf="!isLoading">{{ isEditMode ? 'save' : 'assignment_add' }}</mat-icon>
                {{ isLoading ? 'Đang xử lý...' : (isEditMode ? 'Cập nhật' : 'Mượn sách') }}
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .borrowing-form-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .form-card {
      width: 100%;
    }

    .form-card .mat-mdc-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
    }

    .borrowing-form {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .form-section {
      h3 {
        margin: 0 0 16px 0;
        color: #333;
        font-weight: 500;
        border-bottom: 2px solid #e0e0e0;
        padding-bottom: 8px;
      }
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      &.two-columns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
      }
    }

    .full-width {
      width: 100%;
    }

    .user-option, .book-option {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 8px 0;
    }

    .user-name, .book-title {
      font-weight: 500;
      color: #333;
    }

    .user-email, .book-author {
      font-size: 12px;
      color: #666;
    }

    .book-availability {
      font-size: 11px;
      font-weight: 500;
      
      &.available {
        color: #4caf50;
      }
      
      &.unavailable {
        color: #f44336;
      }
    }

    .borrowing-info {
      background-color: #f5f5f5;
      padding: 16px;
      border-radius: 8px;
      margin-top: 16px;

      h4 {
        margin: 0 0 12px 0;
        color: #333;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      label {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }

      span {
        color: #333;
      }
    }

    .form-actions {
      display: flex;
      gap: 16px;
      justify-content: flex-end;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #e0e0e0;
    }

    .form-actions button {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 120px;
      height: 48px;
    }

    .button-spinner {
      margin-right: 8px;
    }

    @media (max-width: 768px) {
      .borrowing-form-container {
        padding: 16px;
      }

      .form-row.two-columns {
        grid-template-columns: 1fr;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      .form-actions {
        flex-direction: column-reverse;
        
        button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  `]
})
export class BorrowingFormComponent implements OnInit {
  borrowingForm!: FormGroup;
  isEditMode = false;
  isLoading = false;
  borrowingId: number | null = null;
  minDueDate = new Date();

  users: User[] = [];
  books: Book[] = [];
  filteredUsers!: Observable<User[]>;
  filteredBooks!: Observable<Book[]>;
  
  selectedUser: User | null = null;
  selectedBook: Book | null = null;

  constructor(
    private formBuilder: FormBuilder,
    private borrowingService: BorrowingService,
    private userService: UserService,
    private bookService: BookService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadData();
    this.checkEditMode();
    this.setupAutocomplete();
  }

  private initializeForm(): void {
    const today = new Date();
    const defaultDueDate = new Date();
    defaultDueDate.setDate(today.getDate() + DEFAULT_BORROWING_DAYS);

    this.borrowingForm = this.formBuilder.group({
      nguoiDungId: ['', [Validators.required]],
      sachId: ['', [Validators.required]],
      nguoiDungSearch: [''],
      sachSearch: [''],
      ngayMuon: [today],
      ngayHenTra: [defaultDueDate, [Validators.required]],
      ghiChu: ['']
    });
  }

  private loadData(): void {
    // Load active users
    this.userService.getActiveUsers().subscribe({
      next: (users) => {
        this.users = users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
      }
    });

    // Load available books
    this.bookService.getAvailableBooks().subscribe({
      next: (books) => {
        this.books = books;
      },
      error: (error) => {
        console.error('Error loading books:', error);
      }
    });
  }

  private setupAutocomplete(): void {
    this.filteredUsers = this.borrowingForm.get('nguoiDungSearch')!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterUsers(value || ''))
    );

    this.filteredBooks = this.borrowingForm.get('sachSearch')!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterBooks(value || ''))
    );
  }

  private _filterUsers(value: string | User): User[] {
    if (typeof value === 'object') return this.users;
    
    const filterValue = value.toLowerCase();
    return this.users.filter(user => 
      user.hoTen.toLowerCase().includes(filterValue) ||
      user.email.toLowerCase().includes(filterValue)
    );
  }

  private _filterBooks(value: string | Book): Book[] {
    if (typeof value === 'object') return this.books;
    
    const filterValue = value.toLowerCase();
    return this.books.filter(book => 
      book.tenSach.toLowerCase().includes(filterValue) ||
      book.tacGia.toLowerCase().includes(filterValue) ||
      book.isbn.toLowerCase().includes(filterValue)
    );
  }

  displayUser(user: User): string {
    return user ? `${user.hoTen} (${user.email})` : '';
  }

  displayBook(book: Book): string {
    return book ? `${book.tenSach} - ${book.tacGia}` : '';
  }

  onUserSelected(user: User): void {
    this.selectedUser = user;
    this.borrowingForm.patchValue({ nguoiDungId: user.id });
  }

  onBookSelected(book: Book): void {
    this.selectedBook = book;
    this.borrowingForm.patchValue({ sachId: book.id });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.borrowingId = parseInt(id, 10);
      this.loadBorrowing();
    }
  }

  private loadBorrowing(): void {
    if (!this.borrowingId) return;

    this.isLoading = true;
    this.borrowingService.getBorrowingById(this.borrowingId).subscribe({
      next: (borrowing) => {
        this.populateForm(borrowing);
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi tải thông tin mượn sách', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/borrowings']);
      }
    });
  }

  private populateForm(borrowing: Borrowing): void {
    this.borrowingForm.patchValue({
      nguoiDungId: borrowing.nguoiDungId,
      sachId: borrowing.sachId,
      ngayMuon: new Date(borrowing.ngayMuon),
      ngayHenTra: new Date(borrowing.ngayHenTra),
      ghiChu: borrowing.ghiChu
    });

    if (borrowing.nguoiDung) {
      this.selectedUser = borrowing.nguoiDung as any;
      this.borrowingForm.patchValue({
        nguoiDungSearch: this.displayUser(this.selectedUser!)
      });
    }

    if (borrowing.sach) {
      this.selectedBook = borrowing.sach as any;
      this.borrowingForm.patchValue({
        sachSearch: this.displayBook(this.selectedBook!)
      });
    }
  }

  onSubmit(): void {
    if (this.borrowingForm.valid && !this.isLoading) {
      this.isLoading = true;

      if (this.isEditMode) {
        this.updateBorrowing();
      } else {
        this.createBorrowing();
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private createBorrowing(): void {
    const formValue = this.borrowingForm.value;
    const createRequest: CreateBorrowingRequest = {
      nguoiDungId: formValue.nguoiDungId,
      sachId: formValue.sachId,
      ngayHenTra: formValue.ngayHenTra.toISOString().split('T')[0],
      ghiChu: formValue.ghiChu
    };

    this.borrowingService.createBorrowing(createRequest).subscribe({
      next: () => {
        this.isLoading = false;
        this.snackBar.open('Mượn sách thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/borrowings']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi mượn sách', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private updateBorrowing(): void {
    if (!this.borrowingId) return;

    const formValue = this.borrowingForm.value;
    const updateRequest: UpdateBorrowingRequest = {
      ngayHenTra: formValue.ngayHenTra.toISOString().split('T')[0],
      ghiChu: formValue.ghiChu
    };

    this.borrowingService.updateBorrowing(this.borrowingId, updateRequest).subscribe({
      next: () => {
        this.isLoading = false;
        this.snackBar.open('Cập nhật thông tin mượn sách thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/borrowings']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi cập nhật thông tin mượn sách', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/borrowings']);
  }

  getBorrowingDuration(): number {
    const borrowDate = this.borrowingForm.value.ngayMuon;
    const dueDate = this.borrowingForm.value.ngayHenTra;
    
    if (borrowDate && dueDate) {
      const diffTime = Math.abs(dueDate.getTime() - borrowDate.getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    
    return 0;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.borrowingForm.controls).forEach(key => {
      const control = this.borrowingForm.get(key);
      control?.markAsTouched();
    });
  }
}
