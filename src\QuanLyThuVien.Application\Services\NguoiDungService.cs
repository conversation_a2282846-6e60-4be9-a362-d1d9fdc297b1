using BCrypt.Net;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Services;

public class NguoiDungService : INguoiDungService
{
    private readonly INguoiDungRepository _nguoiDungRepository;

    public NguoiDungService(INguoiDungRepository nguoiDungRepository)
    {
        _nguoiDungRepository = nguoiDungRepository;
    }

    public async Task<PagedResult<NguoiDungDto>> GetAllAsync(NguoiDungSearchDto searchDto)
    {
        var result = await _nguoiDungRepository.GetAllAsync(searchDto);
        
        return new PagedResult<NguoiDungDto>
        {
            Items = result.Items.Select(MapToDto).ToList(),
            TotalCount = result.TotalCount,
            Page = result.Page,
            PageSize = result.PageSize
        };
    }

    public async Task<NguoiDungDto?> GetByIdAsync(int id)
    {
        var nguoiDung = await _nguoiDungRepository.GetByIdAsync(id);
        return nguoiDung != null ? MapToDto(nguoiDung) : null;
    }

    public async Task<NguoiDungDto?> GetByEmailAsync(string email)
    {
        var nguoiDung = await _nguoiDungRepository.GetByEmailAsync(email);
        return nguoiDung != null ? MapToDto(nguoiDung) : null;
    }

    public async Task<NguoiDungDto> CreateAsync(CreateNguoiDungDto createDto)
    {
        // Validate email uniqueness
        if (await _nguoiDungRepository.IsEmailExistsAsync(createDto.Email))
        {
            throw new InvalidOperationException($"Email '{createDto.Email}' đã tồn tại trong hệ thống.");
        }

        // Validate SoCanCuoc uniqueness
        if (await _nguoiDungRepository.IsSoCanCuocExistsAsync(createDto.SoCanCuoc))
        {
            throw new InvalidOperationException($"Số căn cước '{createDto.SoCanCuoc}' đã tồn tại trong hệ thống.");
        }

        // Validate age (must be at least 16 years old)
        var age = DateTime.Now.Year - createDto.NgaySinh.Year;
        if (createDto.NgaySinh.Date > DateTime.Now.AddYears(-age)) age--;
        
        if (age < 16)
        {
            throw new InvalidOperationException("Người dùng phải từ 16 tuổi trở lên.");
        }

        var nguoiDung = new NguoiDung
        {
            HoTen = createDto.HoTen,
            Email = createDto.Email,
            SoDienThoai = createDto.SoDienThoai,
            DiaChi = createDto.DiaChi,
            NgaySinh = createDto.NgaySinh,
            GioiTinh = createDto.GioiTinh,
            SoCanCuoc = createDto.SoCanCuoc,
            VaiTro = createDto.VaiTro,
            TrangThaiHoatDong = true,
            MatKhauHash = !string.IsNullOrEmpty(createDto.MatKhau) 
                ? BCrypt.Net.BCrypt.HashPassword(createDto.MatKhau) 
                : null
        };

        var createdNguoiDung = await _nguoiDungRepository.CreateAsync(nguoiDung);
        return MapToDto(createdNguoiDung);
    }

    public async Task<NguoiDungDto> UpdateAsync(int id, UpdateNguoiDungDto updateDto)
    {
        var existingNguoiDung = await _nguoiDungRepository.GetByIdAsync(id);
        if (existingNguoiDung == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy người dùng với ID: {id}");
        }

        // Validate email uniqueness (excluding current record)
        if (await _nguoiDungRepository.IsEmailExistsAsync(updateDto.Email, id))
        {
            throw new InvalidOperationException($"Email '{updateDto.Email}' đã tồn tại trong hệ thống.");
        }

        // Validate SoCanCuoc uniqueness (excluding current record)
        if (await _nguoiDungRepository.IsSoCanCuocExistsAsync(updateDto.SoCanCuoc, id))
        {
            throw new InvalidOperationException($"Số căn cước '{updateDto.SoCanCuoc}' đã tồn tại trong hệ thống.");
        }

        // Validate age (must be at least 16 years old)
        var age = DateTime.Now.Year - updateDto.NgaySinh.Year;
        if (updateDto.NgaySinh.Date > DateTime.Now.AddYears(-age)) age--;
        
        if (age < 16)
        {
            throw new InvalidOperationException("Người dùng phải từ 16 tuổi trở lên.");
        }

        // Update properties
        existingNguoiDung.HoTen = updateDto.HoTen;
        existingNguoiDung.Email = updateDto.Email;
        existingNguoiDung.SoDienThoai = updateDto.SoDienThoai;
        existingNguoiDung.DiaChi = updateDto.DiaChi;
        existingNguoiDung.NgaySinh = updateDto.NgaySinh;
        existingNguoiDung.GioiTinh = updateDto.GioiTinh;
        existingNguoiDung.SoCanCuoc = updateDto.SoCanCuoc;
        existingNguoiDung.VaiTro = updateDto.VaiTro;
        existingNguoiDung.TrangThaiHoatDong = updateDto.TrangThaiHoatDong;

        var updatedNguoiDung = await _nguoiDungRepository.UpdateAsync(existingNguoiDung);
        return MapToDto(updatedNguoiDung);
    }

    public async Task<bool> DeleteAsync(int id)
    {
        if (!await _nguoiDungRepository.ExistsAsync(id))
        {
            throw new KeyNotFoundException($"Không tìm thấy người dùng với ID: {id}");
        }

        // Check if user can be deleted (no active borrowings)
        if (!await _nguoiDungRepository.CanDeleteAsync(id))
        {
            throw new InvalidOperationException("Không thể xóa người dùng đang có sách mượn.");
        }

        return await _nguoiDungRepository.DeleteAsync(id);
    }

    public async Task<bool> ChangePasswordAsync(int id, ChangePasswordDto changePasswordDto)
    {
        var nguoiDung = await _nguoiDungRepository.GetByIdAsync(id);
        if (nguoiDung == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy người dùng với ID: {id}");
        }

        // Verify old password
        if (string.IsNullOrEmpty(nguoiDung.MatKhauHash) || 
            !BCrypt.Net.BCrypt.Verify(changePasswordDto.MatKhauCu, nguoiDung.MatKhauHash))
        {
            throw new InvalidOperationException("Mật khẩu cũ không chính xác.");
        }

        // Update password
        nguoiDung.MatKhauHash = BCrypt.Net.BCrypt.HashPassword(changePasswordDto.MatKhauMoi);
        await _nguoiDungRepository.UpdateAsync(nguoiDung);

        return true;
    }

    public async Task<List<NguoiDungDto>> GetActiveUsersAsync()
    {
        var users = await _nguoiDungRepository.GetActiveUsersAsync();
        return users.Select(MapToDto).ToList();
    }

    public async Task<bool> ValidatePasswordAsync(int id, string password)
    {
        var nguoiDung = await _nguoiDungRepository.GetByIdAsync(id);
        if (nguoiDung == null || string.IsNullOrEmpty(nguoiDung.MatKhauHash))
        {
            return false;
        }

        return BCrypt.Net.BCrypt.Verify(password, nguoiDung.MatKhauHash);
    }

    public async Task<int> GetTotalUsersCountAsync()
    {
        return await _nguoiDungRepository.GetTotalUsersCountAsync();
    }

    public async Task<int> GetActiveUsersCountAsync()
    {
        return await _nguoiDungRepository.GetActiveUsersCountAsync();
    }

    private static NguoiDungDto MapToDto(NguoiDung nguoiDung)
    {
        return new NguoiDungDto
        {
            Id = nguoiDung.Id,
            HoTen = nguoiDung.HoTen,
            Email = nguoiDung.Email,
            SoDienThoai = nguoiDung.SoDienThoai,
            DiaChi = nguoiDung.DiaChi,
            NgaySinh = nguoiDung.NgaySinh,
            GioiTinh = nguoiDung.GioiTinh,
            SoCanCuoc = nguoiDung.SoCanCuoc,
            NgayDangKy = nguoiDung.NgayDangKy,
            TrangThaiHoatDong = nguoiDung.TrangThaiHoatDong,
            VaiTro = nguoiDung.VaiTro,
            NgayTao = nguoiDung.NgayTao,
            NgayCapNhat = nguoiDung.NgayCapNhat
        };
    }
}
