{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/icon-registry-B2IMBfNA.mjs", "../../../../../../node_modules/@angular/material/fesm2022/icon.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, SkipSelf, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { of, throwError, forkJoin } from 'rxjs';\nimport { tap, map, catchError, finalize, share } from 'rxjs/operators';\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n  if (policy === undefined) {\n    policy = null;\n    if (typeof window !== 'undefined') {\n      const ttWindow = window;\n      if (ttWindow.trustedTypes !== undefined) {\n        policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n          createHTML: s => s\n        });\n      }\n    }\n  }\n  return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n  return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n  return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n  return Error('Could not find HttpClient for use with Angular Material icons. ' + 'Please add provideHttpClient() to your providers.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n  return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` + `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n  return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` + `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n  url;\n  svgText;\n  options;\n  svgElement;\n  constructor(url, svgText, options) {\n    this.url = url;\n    this.svgText = svgText;\n    this.options = options;\n  }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n  _httpClient;\n  _sanitizer;\n  _errorHandler;\n  _document;\n  /**\n   * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n   */\n  _svgIconConfigs = new Map();\n  /**\n   * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n   * Multiple icon sets can be registered under the same namespace.\n   */\n  _iconSetConfigs = new Map();\n  /** Cache for icons loaded by direct URLs. */\n  _cachedIconsByUrl = new Map();\n  /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n  _inProgressUrlFetches = new Map();\n  /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n  _fontCssClassesByAlias = new Map();\n  /** Registered icon resolver functions. */\n  _resolvers = [];\n  /**\n   * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n   * specified. The default 'material-icons' value assumes that the material icon font has been\n   * loaded as described at https://google.github.io/material-design-icons/#icon-font-for-the-web\n   */\n  _defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n  constructor(_httpClient, _sanitizer, document, _errorHandler) {\n    this._httpClient = _httpClient;\n    this._sanitizer = _sanitizer;\n    this._errorHandler = _errorHandler;\n    this._document = document;\n  }\n  /**\n   * Registers an icon by URL in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIcon(iconName, url, options) {\n    return this.addSvgIconInNamespace('', iconName, url, options);\n  }\n  /**\n   * Registers an icon using an HTML string in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteral(iconName, literal, options) {\n    return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n  }\n  /**\n   * Registers an icon by URL in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIconInNamespace(namespace, iconName, url, options) {\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon resolver function with the registry. The function will be invoked with the\n   * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n   * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n   * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n   * will be invoked in the order in which they have been registered.\n   * @param resolver Resolver function to be registered.\n   */\n  addSvgIconResolver(resolver) {\n    this._resolvers.push(resolver);\n    return this;\n  }\n  /**\n   * Registers an icon using an HTML string in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n    // TODO: add an ngDevMode check\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Registers an icon set by URL in the default namespace.\n   * @param url\n   */\n  addSvgIconSet(url, options) {\n    return this.addSvgIconSetInNamespace('', url, options);\n  }\n  /**\n   * Registers an icon set using an HTML string in the default namespace.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteral(literal, options) {\n    return this.addSvgIconSetLiteralInNamespace('', literal, options);\n  }\n  /**\n   * Registers an icon set by URL in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param url\n   */\n  addSvgIconSetInNamespace(namespace, url, options) {\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon set using an HTML string in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n   * component with the alias as the fontSet input will cause the class name to be applied\n   * to the `<mat-icon>` element.\n   *\n   * If the registered font is a ligature font, then don't forget to also include the special\n   * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n   *\n   * ```ts\n   * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n   * ```\n   *\n   * And use like this:\n   *\n   * ```html\n   * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n   * ```\n   *\n   * @param alias Alias for the font.\n   * @param classNames Class names override to be used instead of the alias.\n   */\n  registerFontClassAlias(alias, classNames = alias) {\n    this._fontCssClassesByAlias.set(alias, classNames);\n    return this;\n  }\n  /**\n   * Returns the CSS class name associated with the alias by a previous call to\n   * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n   */\n  classNameForFontAlias(alias) {\n    return this._fontCssClassesByAlias.get(alias) || alias;\n  }\n  /**\n   * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  setDefaultFontSetClass(...classNames) {\n    this._defaultFontSetClass = classNames;\n    return this;\n  }\n  /**\n   * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  getDefaultFontSetClass() {\n    return this._defaultFontSetClass;\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n   * The response from the URL may be cached so this will not always cause an HTTP request, but\n   * the produced element will always be a new copy of the originally fetched icon. (That is,\n   * it will not contain any modifications made to elements previously returned).\n   *\n   * @param safeUrl URL from which to fetch the SVG icon.\n   */\n  getSvgIconFromUrl(safeUrl) {\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n    const cachedIcon = this._cachedIconsByUrl.get(url);\n    if (cachedIcon) {\n      return of(cloneSvg(cachedIcon));\n    }\n    return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n   * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n   * if not, the Observable will throw an error.\n   *\n   * @param name Name of the icon to be retrieved.\n   * @param namespace Namespace in which to look for the icon.\n   */\n  getNamedSvgIcon(name, namespace = '') {\n    const key = iconKey(namespace, name);\n    let config = this._svgIconConfigs.get(key);\n    // Return (copy of) cached icon if possible.\n    if (config) {\n      return this._getSvgFromConfig(config);\n    }\n    // Otherwise try to resolve the config from one of the resolver functions.\n    config = this._getIconConfigFromResolvers(namespace, name);\n    if (config) {\n      this._svgIconConfigs.set(key, config);\n      return this._getSvgFromConfig(config);\n    }\n    // See if we have any icon sets registered for the namespace.\n    const iconSetConfigs = this._iconSetConfigs.get(namespace);\n    if (iconSetConfigs) {\n      return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n    }\n    return throwError(getMatIconNameNotFoundError(key));\n  }\n  ngOnDestroy() {\n    this._resolvers = [];\n    this._svgIconConfigs.clear();\n    this._iconSetConfigs.clear();\n    this._cachedIconsByUrl.clear();\n  }\n  /**\n   * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n   */\n  _getSvgFromConfig(config) {\n    if (config.svgText) {\n      // We already have the SVG element for this icon, return a copy.\n      return of(cloneSvg(this._svgElementFromConfig(config)));\n    } else {\n      // Fetch the icon from the config's URL, cache it, and return a copy.\n      return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n    }\n  }\n  /**\n   * Attempts to find an icon with the specified name in any of the SVG icon sets.\n   * First searches the available cached icons for a nested element with a matching name, and\n   * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n   * that have not been cached, and searches again after all fetches are completed.\n   * The returned Observable produces the SVG element if possible, and throws\n   * an error if no icon with the specified name can be found.\n   */\n  _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n    // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n    // requested name.\n    const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n    if (namedIcon) {\n      // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n      // time anyway, there's probably not much advantage compared to just always extracting\n      // it from the icon set.\n      return of(namedIcon);\n    }\n    // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n    // fetched, fetch them now and look for iconName in the results.\n    const iconSetFetchRequests = iconSetConfigs.filter(iconSetConfig => !iconSetConfig.svgText).map(iconSetConfig => {\n      return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError(err => {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n        // Swallow errors fetching individual URLs so the\n        // combined Observable won't necessarily fail.\n        const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n        this._errorHandler.handleError(new Error(errorMessage));\n        return of(null);\n      }));\n    });\n    // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n    // cached SVG element (unless the request failed), and we can check again for the icon.\n    return forkJoin(iconSetFetchRequests).pipe(map(() => {\n      const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n      // TODO: add an ngDevMode check\n      if (!foundIcon) {\n        throw getMatIconNameNotFoundError(name);\n      }\n      return foundIcon;\n    }));\n  }\n  /**\n   * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n    // Iterate backwards, so icon sets added later have precedence.\n    for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n      const config = iconSetConfigs[i];\n      // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n      // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n      // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n      // some of the parsing.\n      if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n        const svg = this._svgElementFromConfig(config);\n        const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n        if (foundIcon) {\n          return foundIcon;\n        }\n      }\n    }\n    return null;\n  }\n  /**\n   * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n   * from it.\n   */\n  _loadSvgIconFromConfig(config) {\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText), map(() => this._svgElementFromConfig(config)));\n  }\n  /**\n   * Loads the content of the icon set URL specified in the\n   * SvgIconConfig and attaches it to the config.\n   */\n  _loadSvgIconSetFromConfig(config) {\n    if (config.svgText) {\n      return of(null);\n    }\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText));\n  }\n  /**\n   * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  _extractSvgIconFromSet(iconSet, iconName, options) {\n    // Use the `id=\"iconName\"` syntax in order to escape special\n    // characters in the ID (versus using the #iconName syntax).\n    const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n    if (!iconSource) {\n      return null;\n    }\n    // Clone the element and remove the ID to prevent multiple elements from being added\n    // to the page with the same ID.\n    const iconElement = iconSource.cloneNode(true);\n    iconElement.removeAttribute('id');\n    // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n    // the content of a new <svg> node.\n    if (iconElement.nodeName.toLowerCase() === 'svg') {\n      return this._setSvgAttributes(iconElement, options);\n    }\n    // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n    // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n    // tag is problematic on Firefox, because it needs to include the current page path.\n    if (iconElement.nodeName.toLowerCase() === 'symbol') {\n      return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n    }\n    // createElement('SVG') doesn't work as expected; the DOM ends up with\n    // the correct nodes, but the SVG content doesn't render. Instead we\n    // have to create an empty SVG node using innerHTML and append its content.\n    // Elements created using DOMParser.parseFromString have the same problem.\n    // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    // Clone the node so we don't remove it from the parent icon set element.\n    svg.appendChild(iconElement);\n    return this._setSvgAttributes(svg, options);\n  }\n  /**\n   * Creates a DOM element from the given SVG string.\n   */\n  _svgElementFromString(str) {\n    const div = this._document.createElement('DIV');\n    div.innerHTML = str;\n    const svg = div.querySelector('svg');\n    // TODO: add an ngDevMode check\n    if (!svg) {\n      throw Error('<svg> tag not found');\n    }\n    return svg;\n  }\n  /**\n   * Converts an element into an SVG node by cloning all of its children.\n   */\n  _toSvgElement(element) {\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    const attributes = element.attributes;\n    // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n    for (let i = 0; i < attributes.length; i++) {\n      const {\n        name,\n        value\n      } = attributes[i];\n      if (name !== 'id') {\n        svg.setAttribute(name, value);\n      }\n    }\n    for (let i = 0; i < element.childNodes.length; i++) {\n      if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n        svg.appendChild(element.childNodes[i].cloneNode(true));\n      }\n    }\n    return svg;\n  }\n  /**\n   * Sets the default attributes for an SVG element to be used as an icon.\n   */\n  _setSvgAttributes(svg, options) {\n    svg.setAttribute('fit', '');\n    svg.setAttribute('height', '100%');\n    svg.setAttribute('width', '100%');\n    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n    svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n    if (options && options.viewBox) {\n      svg.setAttribute('viewBox', options.viewBox);\n    }\n    return svg;\n  }\n  /**\n   * Returns an Observable which produces the string contents of the given icon. Results may be\n   * cached, so future calls with the same URL may not cause another HTTP request.\n   */\n  _fetchIcon(iconConfig) {\n    const {\n      url: safeUrl,\n      options\n    } = iconConfig;\n    const withCredentials = options?.withCredentials ?? false;\n    if (!this._httpClient) {\n      throw getMatIconNoHttpProviderError();\n    }\n    // TODO: add an ngDevMode check\n    if (safeUrl == null) {\n      throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n    }\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n    // TODO: add an ngDevMode check\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n    // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n    // already a request in progress for that URL. It's necessary to call share() on the\n    // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n    const inProgressFetch = this._inProgressUrlFetches.get(url);\n    if (inProgressFetch) {\n      return inProgressFetch;\n    }\n    const req = this._httpClient.get(url, {\n      responseType: 'text',\n      withCredentials\n    }).pipe(map(svg => {\n      // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n      // trusted HTML.\n      return trustedHTMLFromString(svg);\n    }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n    this._inProgressUrlFetches.set(url, req);\n    return req;\n  }\n  /**\n   * Registers an icon config by name in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param iconName Name under which to register the config.\n   * @param config Config to be registered.\n   */\n  _addSvgIconConfig(namespace, iconName, config) {\n    this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n    return this;\n  }\n  /**\n   * Registers an icon set config in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param config Config to be registered.\n   */\n  _addSvgIconSetConfig(namespace, config) {\n    const configNamespace = this._iconSetConfigs.get(namespace);\n    if (configNamespace) {\n      configNamespace.push(config);\n    } else {\n      this._iconSetConfigs.set(namespace, [config]);\n    }\n    return this;\n  }\n  /** Parses a config's text into an SVG element. */\n  _svgElementFromConfig(config) {\n    if (!config.svgElement) {\n      const svg = this._svgElementFromString(config.svgText);\n      this._setSvgAttributes(svg, config.options);\n      config.svgElement = svg;\n    }\n    return config.svgElement;\n  }\n  /** Tries to create an icon config through the registered resolver functions. */\n  _getIconConfigFromResolvers(namespace, name) {\n    for (let i = 0; i < this._resolvers.length; i++) {\n      const result = this._resolvers[i](name, namespace);\n      if (result) {\n        return isSafeUrlWithOptions(result) ? new SvgIconConfig(result.url, null, result.options) : new SvgIconConfig(result, null);\n      }\n    }\n    return undefined;\n  }\n  static ɵfac = function MatIconRegistry_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatIconRegistry)(i0.ɵɵinject(i1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(i0.ErrorHandler));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatIconRegistry,\n    factory: MatIconRegistry.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.HttpClient,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.DomSanitizer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ErrorHandler\n  }], null);\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n  return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst ICON_REGISTRY_PROVIDER = {\n  // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n  provide: MatIconRegistry,\n  deps: [[new Optional(), new SkipSelf(), MatIconRegistry], [new Optional(), HttpClient], DomSanitizer, ErrorHandler, [new Optional(), DOCUMENT]],\n  useFactory: ICON_REGISTRY_PROVIDER_FACTORY\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n  return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n  return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n  return !!(value.url && value.options);\n}\nexport { ICON_REGISTRY_PROVIDER_FACTORY as I, MatIconRegistry as M, getMatIconNoHttpProviderError as a, getMatIconFailedToSanitizeUrlError as b, getMatIconFailedToSanitizeLiteralError as c, ICON_REGISTRY_PROVIDER as d, getMatIconNameNotFoundError as g };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HostAttributeToken, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { M as MatIconRegistry } from './icon-registry-B2IMBfNA.mjs';\nconst _c0 = [\"*\"];\nexport { d as ICON_REGISTRY_PROVIDER, I as ICON_REGISTRY_PROVIDER_FACTORY, c as getMatIconFailedToSanitizeLiteralError, b as getMatIconFailedToSanitizeUrlError, g as getMatIconNameNotFoundError, a as getMatIconNoHttpProviderError } from './icon-registry-B2IMBfNA.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/common/http';\nimport '@angular/platform-browser';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n  providedIn: 'root',\n  factory: MAT_ICON_LOCATION_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_ICON_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = ['clip-path', 'color-profile', 'src', 'cursor', 'fill', 'filter', 'marker', 'marker-start', 'marker-mid', 'marker-end', 'mask', 'stroke'];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fontawesome-v4.github.io/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon {\n  _elementRef = inject(ElementRef);\n  _iconRegistry = inject(MatIconRegistry);\n  _location = inject(MAT_ICON_LOCATION);\n  _errorHandler = inject(ErrorHandler);\n  _defaultColor;\n  /**\n   * Theme color of the icon. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/icon/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  _color;\n  /**\n   * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n   * the element the icon is contained in.\n   */\n  inline = false;\n  /** Name of the icon in the SVG icon set. */\n  get svgIcon() {\n    return this._svgIcon;\n  }\n  set svgIcon(value) {\n    if (value !== this._svgIcon) {\n      if (value) {\n        this._updateSvgIcon(value);\n      } else if (this._svgIcon) {\n        this._clearSvgElement();\n      }\n      this._svgIcon = value;\n    }\n  }\n  _svgIcon;\n  /** Font set that the icon is a part of. */\n  get fontSet() {\n    return this._fontSet;\n  }\n  set fontSet(value) {\n    const newValue = this._cleanupFontValue(value);\n    if (newValue !== this._fontSet) {\n      this._fontSet = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  _fontSet;\n  /** Name of an icon within a font set. */\n  get fontIcon() {\n    return this._fontIcon;\n  }\n  set fontIcon(value) {\n    const newValue = this._cleanupFontValue(value);\n    if (newValue !== this._fontIcon) {\n      this._fontIcon = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  _fontIcon;\n  _previousFontSetClass = [];\n  _previousFontIconClass;\n  _svgName;\n  _svgNamespace;\n  /** Keeps track of the current page path. */\n  _previousPath;\n  /** Keeps track of the elements and attributes that we've prefixed with the current path. */\n  _elementsWithExternalReferences;\n  /** Subscription to the current in-progress SVG icon request. */\n  _currentIconFetch = Subscription.EMPTY;\n  constructor() {\n    const ariaHidden = inject(new HostAttributeToken('aria-hidden'), {\n      optional: true\n    });\n    const defaults = inject(MAT_ICON_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.fontSet) {\n        this.fontSet = defaults.fontSet;\n      }\n    }\n    // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n    // the right thing to do for the majority of icon use-cases.\n    if (!ariaHidden) {\n      this._elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n    }\n  }\n  /**\n   * Splits an svgIcon binding value into its icon set and icon name components.\n   * Returns a 2-element array of [(icon set), (icon name)].\n   * The separator for the two fields is ':'. If there is no separator, an empty\n   * string is returned for the icon set and the entire value is returned for\n   * the icon name. If the argument is falsy, returns an array of two empty strings.\n   * Throws an error if the name contains two or more ':' separators.\n   * Examples:\n   *   `'social:cake' -> ['social', 'cake']\n   *   'penguin' -> ['', 'penguin']\n   *   null -> ['', '']\n   *   'a:b:c' -> (throws Error)`\n   */\n  _splitIconName(iconName) {\n    if (!iconName) {\n      return ['', ''];\n    }\n    const parts = iconName.split(':');\n    switch (parts.length) {\n      case 1:\n        return ['', parts[0]];\n      // Use default namespace.\n      case 2:\n        return parts;\n      default:\n        throw Error(`Invalid icon name: \"${iconName}\"`);\n      // TODO: add an ngDevMode check\n    }\n  }\n  ngOnInit() {\n    // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n    // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n    this._updateFontIconClasses();\n  }\n  ngAfterViewChecked() {\n    const cachedElements = this._elementsWithExternalReferences;\n    if (cachedElements && cachedElements.size) {\n      const newPath = this._location.getPathname();\n      // We need to check whether the URL has changed on each change detection since\n      // the browser doesn't have an API that will let us react on link clicks and\n      // we can't depend on the Angular router. The references need to be updated,\n      // because while most browsers don't care whether the URL is correct after\n      // the first render, Safari will break if the user navigates to a different\n      // page and the SVG isn't re-rendered.\n      if (newPath !== this._previousPath) {\n        this._previousPath = newPath;\n        this._prependPathToReferences(newPath);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._currentIconFetch.unsubscribe();\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n  }\n  _usingFontIcon() {\n    return !this.svgIcon;\n  }\n  _setSvgElement(svg) {\n    this._clearSvgElement();\n    // Note: we do this fix here, rather than the icon registry, because the\n    // references have to point to the URL at the time that the icon was created.\n    const path = this._location.getPathname();\n    this._previousPath = path;\n    this._cacheChildrenWithExternalReferences(svg);\n    this._prependPathToReferences(path);\n    this._elementRef.nativeElement.appendChild(svg);\n  }\n  _clearSvgElement() {\n    const layoutElement = this._elementRef.nativeElement;\n    let childCount = layoutElement.childNodes.length;\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n    // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n    // we can't use innerHTML, because IE will throw if the element has a data binding.\n    while (childCount--) {\n      const child = layoutElement.childNodes[childCount];\n      // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n      // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n      if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n        child.remove();\n      }\n    }\n  }\n  _updateFontIconClasses() {\n    if (!this._usingFontIcon()) {\n      return;\n    }\n    const elem = this._elementRef.nativeElement;\n    const fontSetClasses = (this.fontSet ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/) : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n    this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n    fontSetClasses.forEach(className => elem.classList.add(className));\n    this._previousFontSetClass = fontSetClasses;\n    if (this.fontIcon !== this._previousFontIconClass && !fontSetClasses.includes('mat-ligature-font')) {\n      if (this._previousFontIconClass) {\n        elem.classList.remove(this._previousFontIconClass);\n      }\n      if (this.fontIcon) {\n        elem.classList.add(this.fontIcon);\n      }\n      this._previousFontIconClass = this.fontIcon;\n    }\n  }\n  /**\n   * Cleans up a value to be used as a fontIcon or fontSet.\n   * Since the value ends up being assigned as a CSS class, we\n   * have to trim the value and omit space-separated values.\n   */\n  _cleanupFontValue(value) {\n    return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n  }\n  /**\n   * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n   * reference. This is required because WebKit browsers require references to be prefixed with\n   * the current path, if the page has a `base` tag.\n   */\n  _prependPathToReferences(path) {\n    const elements = this._elementsWithExternalReferences;\n    if (elements) {\n      elements.forEach((attrs, element) => {\n        attrs.forEach(attr => {\n          element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n        });\n      });\n    }\n  }\n  /**\n   * Caches the children of an SVG element that have `url()`\n   * references that we need to prefix with the current path.\n   */\n  _cacheChildrenWithExternalReferences(element) {\n    const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n    const elements = this._elementsWithExternalReferences = this._elementsWithExternalReferences || new Map();\n    for (let i = 0; i < elementsWithFuncIri.length; i++) {\n      funcIriAttributes.forEach(attr => {\n        const elementWithReference = elementsWithFuncIri[i];\n        const value = elementWithReference.getAttribute(attr);\n        const match = value ? value.match(funcIriPattern) : null;\n        if (match) {\n          let attributes = elements.get(elementWithReference);\n          if (!attributes) {\n            attributes = [];\n            elements.set(elementWithReference, attributes);\n          }\n          attributes.push({\n            name: attr,\n            value: match[1]\n          });\n        }\n      });\n    }\n  }\n  /** Sets a new SVG icon with a particular name. */\n  _updateSvgIcon(rawName) {\n    this._svgNamespace = null;\n    this._svgName = null;\n    this._currentIconFetch.unsubscribe();\n    if (rawName) {\n      const [namespace, iconName] = this._splitIconName(rawName);\n      if (namespace) {\n        this._svgNamespace = namespace;\n      }\n      if (iconName) {\n        this._svgName = iconName;\n      }\n      this._currentIconFetch = this._iconRegistry.getNamedSvgIcon(iconName, namespace).pipe(take(1)).subscribe(svg => this._setSvgElement(svg), err => {\n        const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n        this._errorHandler.handleError(new Error(errorMessage));\n      });\n    }\n  }\n  static ɵfac = function MatIcon_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatIcon)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatIcon,\n    selectors: [[\"mat-icon\"]],\n    hostAttrs: [\"role\", \"img\", 1, \"mat-icon\", \"notranslate\"],\n    hostVars: 10,\n    hostBindings: function MatIcon_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"data-mat-icon-type\", ctx._usingFontIcon() ? \"font\" : \"svg\")(\"data-mat-icon-name\", ctx._svgName || ctx.fontIcon)(\"data-mat-icon-namespace\", ctx._svgNamespace || ctx.fontSet)(\"fontIcon\", ctx._usingFontIcon() ? ctx.fontIcon : null);\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-icon-inline\", ctx.inline)(\"mat-icon-no-color\", ctx.color !== \"primary\" && ctx.color !== \"accent\" && ctx.color !== \"warn\");\n      }\n    },\n    inputs: {\n      color: \"color\",\n      inline: [2, \"inline\", \"inline\", booleanAttribute],\n      svgIcon: \"svgIcon\",\n      fontSet: \"fontSet\",\n      fontIcon: \"fontIcon\"\n    },\n    exportAs: [\"matIcon\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIcon, [{\n    type: Component,\n    args: [{\n      template: '<ng-content></ng-content>',\n      selector: 'mat-icon',\n      exportAs: 'matIcon',\n      host: {\n        'role': 'img',\n        'class': 'mat-icon notranslate',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n        '[class.mat-icon-inline]': 'inline',\n        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    svgIcon: [{\n      type: Input\n    }],\n    fontSet: [{\n      type: Input\n    }],\n    fontIcon: [{\n      type: Input\n    }]\n  });\n})();\nclass MatIconModule {\n  static ɵfac = function MatIconModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatIconModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatIconModule,\n    imports: [MatCommonModule, MatIcon],\n    exports: [MatIcon, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatIcon],\n      exports: [MatIcon, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAI;AAKJ,SAAS,YAAY;AACnB,MAAI,WAAW,QAAW;AACxB,aAAS;AACT,QAAI,OAAO,WAAW,aAAa;AACjC,YAAM,WAAW;AACjB,UAAI,SAAS,iBAAiB,QAAW;AACvC,iBAAS,SAAS,aAAa,aAAa,sBAAsB;AAAA,UAChE,YAAY,OAAK;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAUA,SAAS,sBAAsB,MAAM;AACnC,SAAO,UAAU,GAAG,WAAW,IAAI,KAAK;AAC1C;AAOA,SAAS,4BAA4B,UAAU;AAC7C,SAAO,MAAM,sCAAsC,QAAQ,GAAG;AAChE;AAMA,SAAS,gCAAgC;AACvC,SAAO,MAAM,kHAAuH;AACtI;AAMA,SAAS,mCAAmC,KAAK;AAC/C,SAAO,MAAM,wHAA6H,GAAG,IAAI;AACnJ;AAMA,SAAS,uCAAuC,SAAS;AACvD,SAAO,MAAM,0HAA+H,OAAO,IAAI;AACzJ;AAKA,IAAM,gBAAN,MAAoB;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,SAAS,SAAS;AACjC,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AACF;AAQA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,kBAAkB,oBAAI,IAAI;AAAA;AAAA,EAE1B,oBAAoB,oBAAI,IAAI;AAAA;AAAA,EAE5B,wBAAwB,oBAAI,IAAI;AAAA;AAAA,EAEhC,yBAAyB,oBAAI,IAAI;AAAA;AAAA,EAEjC,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,uBAAuB,CAAC,kBAAkB,mBAAmB;AAAA,EAC7D,YAAY,aAAa,YAAY,UAAU,eAAe;AAC5D,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,UAAU,KAAK,SAAS;AACjC,WAAO,KAAK,sBAAsB,IAAI,UAAU,KAAK,OAAO;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,UAAU,SAAS,SAAS;AAC5C,WAAO,KAAK,6BAA6B,IAAI,UAAU,SAAS,OAAO;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,WAAW,UAAU,KAAK,SAAS;AACvD,WAAO,KAAK,kBAAkB,WAAW,UAAU,IAAI,cAAc,KAAK,MAAM,OAAO,CAAC;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,UAAU;AAC3B,SAAK,WAAW,KAAK,QAAQ;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,6BAA6B,WAAW,UAAU,SAAS,SAAS;AAClE,UAAM,eAAe,KAAK,WAAW,SAAS,gBAAgB,MAAM,OAAO;AAE3E,QAAI,CAAC,cAAc;AACjB,YAAM,uCAAuC,OAAO;AAAA,IACtD;AAEA,UAAM,iBAAiB,sBAAsB,YAAY;AACzD,WAAO,KAAK,kBAAkB,WAAW,UAAU,IAAI,cAAc,IAAI,gBAAgB,OAAO,CAAC;AAAA,EACnG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK,SAAS;AAC1B,WAAO,KAAK,yBAAyB,IAAI,KAAK,OAAO;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,SAAS,SAAS;AACrC,WAAO,KAAK,gCAAgC,IAAI,SAAS,OAAO;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,WAAW,KAAK,SAAS;AAChD,WAAO,KAAK,qBAAqB,WAAW,IAAI,cAAc,KAAK,MAAM,OAAO,CAAC;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gCAAgC,WAAW,SAAS,SAAS;AAC3D,UAAM,eAAe,KAAK,WAAW,SAAS,gBAAgB,MAAM,OAAO;AAC3E,QAAI,CAAC,cAAc;AACjB,YAAM,uCAAuC,OAAO;AAAA,IACtD;AAEA,UAAM,iBAAiB,sBAAsB,YAAY;AACzD,WAAO,KAAK,qBAAqB,WAAW,IAAI,cAAc,IAAI,gBAAgB,OAAO,CAAC;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,uBAAuB,OAAO,aAAa,OAAO;AAChD,SAAK,uBAAuB,IAAI,OAAO,UAAU;AACjD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,OAAO;AAC3B,WAAO,KAAK,uBAAuB,IAAI,KAAK,KAAK;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,YAAY;AACpC,SAAK,uBAAuB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAkB,SAAS;AACzB,UAAM,MAAM,KAAK,WAAW,SAAS,gBAAgB,cAAc,OAAO;AAC1E,QAAI,CAAC,KAAK;AACR,YAAM,mCAAmC,OAAO;AAAA,IAClD;AACA,UAAM,aAAa,KAAK,kBAAkB,IAAI,GAAG;AACjD,QAAI,YAAY;AACd,aAAO,GAAG,SAAS,UAAU,CAAC;AAAA,IAChC;AACA,WAAO,KAAK,uBAAuB,IAAI,cAAc,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,SAAO,KAAK,kBAAkB,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,SAAO,SAAS,GAAG,CAAC,CAAC;AAAA,EACvJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,MAAM,YAAY,IAAI;AACpC,UAAM,MAAM,QAAQ,WAAW,IAAI;AACnC,QAAI,SAAS,KAAK,gBAAgB,IAAI,GAAG;AAEzC,QAAI,QAAQ;AACV,aAAO,KAAK,kBAAkB,MAAM;AAAA,IACtC;AAEA,aAAS,KAAK,4BAA4B,WAAW,IAAI;AACzD,QAAI,QAAQ;AACV,WAAK,gBAAgB,IAAI,KAAK,MAAM;AACpC,aAAO,KAAK,kBAAkB,MAAM;AAAA,IACtC;AAEA,UAAM,iBAAiB,KAAK,gBAAgB,IAAI,SAAS;AACzD,QAAI,gBAAgB;AAClB,aAAO,KAAK,0BAA0B,MAAM,cAAc;AAAA,IAC5D;AACA,WAAO,WAAW,4BAA4B,GAAG,CAAC;AAAA,EACpD;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,CAAC;AACnB,SAAK,gBAAgB,MAAM;AAC3B,SAAK,gBAAgB,MAAM;AAC3B,SAAK,kBAAkB,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ;AACxB,QAAI,OAAO,SAAS;AAElB,aAAO,GAAG,SAAS,KAAK,sBAAsB,MAAM,CAAC,CAAC;AAAA,IACxD,OAAO;AAEL,aAAO,KAAK,uBAAuB,MAAM,EAAE,KAAK,IAAI,SAAO,SAAS,GAAG,CAAC,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,0BAA0B,MAAM,gBAAgB;AAG9C,UAAM,YAAY,KAAK,+BAA+B,MAAM,cAAc;AAC1E,QAAI,WAAW;AAIb,aAAO,GAAG,SAAS;AAAA,IACrB;AAGA,UAAM,uBAAuB,eAAe,OAAO,mBAAiB,CAAC,cAAc,OAAO,EAAE,IAAI,mBAAiB;AAC/G,aAAO,KAAK,0BAA0B,aAAa,EAAE,KAAK,WAAW,SAAO;AAC1E,cAAM,MAAM,KAAK,WAAW,SAAS,gBAAgB,cAAc,cAAc,GAAG;AAGpF,cAAM,eAAe,yBAAyB,GAAG,YAAY,IAAI,OAAO;AACxE,aAAK,cAAc,YAAY,IAAI,MAAM,YAAY,CAAC;AACtD,eAAO,GAAG,IAAI;AAAA,MAChB,CAAC,CAAC;AAAA,IACJ,CAAC;AAGD,WAAO,SAAS,oBAAoB,EAAE,KAAK,IAAI,MAAM;AACnD,YAAM,YAAY,KAAK,+BAA+B,MAAM,cAAc;AAE1E,UAAI,CAAC,WAAW;AACd,cAAM,4BAA4B,IAAI;AAAA,MACxC;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,+BAA+B,UAAU,gBAAgB;AAEvD,aAAS,IAAI,eAAe,SAAS,GAAG,KAAK,GAAG,KAAK;AACnD,YAAM,SAAS,eAAe,CAAC;AAK/B,UAAI,OAAO,WAAW,OAAO,QAAQ,SAAS,EAAE,QAAQ,QAAQ,IAAI,IAAI;AACtE,cAAM,MAAM,KAAK,sBAAsB,MAAM;AAC7C,cAAM,YAAY,KAAK,uBAAuB,KAAK,UAAU,OAAO,OAAO;AAC3E,YAAI,WAAW;AACb,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,QAAQ;AAC7B,WAAO,KAAK,WAAW,MAAM,EAAE,KAAK,IAAI,aAAW,OAAO,UAAU,OAAO,GAAG,IAAI,MAAM,KAAK,sBAAsB,MAAM,CAAC,CAAC;AAAA,EAC7H;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,QAAQ;AAChC,QAAI,OAAO,SAAS;AAClB,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,WAAO,KAAK,WAAW,MAAM,EAAE,KAAK,IAAI,aAAW,OAAO,UAAU,OAAO,CAAC;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,SAAS,UAAU,SAAS;AAGjD,UAAM,aAAa,QAAQ,cAAc,QAAQ,QAAQ,IAAI;AAC7D,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AAGA,UAAM,cAAc,WAAW,UAAU,IAAI;AAC7C,gBAAY,gBAAgB,IAAI;AAGhC,QAAI,YAAY,SAAS,YAAY,MAAM,OAAO;AAChD,aAAO,KAAK,kBAAkB,aAAa,OAAO;AAAA,IACpD;AAIA,QAAI,YAAY,SAAS,YAAY,MAAM,UAAU;AACnD,aAAO,KAAK,kBAAkB,KAAK,cAAc,WAAW,GAAG,OAAO;AAAA,IACxE;AAMA,UAAM,MAAM,KAAK,sBAAsB,sBAAsB,aAAa,CAAC;AAE3E,QAAI,YAAY,WAAW;AAC3B,WAAO,KAAK,kBAAkB,KAAK,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,KAAK;AACzB,UAAM,MAAM,KAAK,UAAU,cAAc,KAAK;AAC9C,QAAI,YAAY;AAChB,UAAM,MAAM,IAAI,cAAc,KAAK;AAEnC,QAAI,CAAC,KAAK;AACR,YAAM,MAAM,qBAAqB;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,SAAS;AACrB,UAAM,MAAM,KAAK,sBAAsB,sBAAsB,aAAa,CAAC;AAC3E,UAAM,aAAa,QAAQ;AAE3B,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,WAAW,CAAC;AAChB,UAAI,SAAS,MAAM;AACjB,YAAI,aAAa,MAAM,KAAK;AAAA,MAC9B;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,WAAW,QAAQ,KAAK;AAClD,UAAI,QAAQ,WAAW,CAAC,EAAE,aAAa,KAAK,UAAU,cAAc;AAClE,YAAI,YAAY,QAAQ,WAAW,CAAC,EAAE,UAAU,IAAI,CAAC;AAAA,MACvD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,KAAK,SAAS;AAC9B,QAAI,aAAa,OAAO,EAAE;AAC1B,QAAI,aAAa,UAAU,MAAM;AACjC,QAAI,aAAa,SAAS,MAAM;AAChC,QAAI,aAAa,uBAAuB,eAAe;AACvD,QAAI,aAAa,aAAa,OAAO;AACrC,QAAI,WAAW,QAAQ,SAAS;AAC9B,UAAI,aAAa,WAAW,QAAQ,OAAO;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,YAAY;AACrB,UAAM;AAAA,MACJ,KAAK;AAAA,MACL;AAAA,IACF,IAAI;AACJ,UAAM,kBAAkB,SAAS,mBAAmB;AACpD,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,8BAA8B;AAAA,IACtC;AAEA,QAAI,WAAW,MAAM;AACnB,YAAM,MAAM,+BAA+B,OAAO,IAAI;AAAA,IACxD;AACA,UAAM,MAAM,KAAK,WAAW,SAAS,gBAAgB,cAAc,OAAO;AAE1E,QAAI,CAAC,KAAK;AACR,YAAM,mCAAmC,OAAO;AAAA,IAClD;AAIA,UAAM,kBAAkB,KAAK,sBAAsB,IAAI,GAAG;AAC1D,QAAI,iBAAiB;AACnB,aAAO;AAAA,IACT;AACA,UAAM,MAAM,KAAK,YAAY,IAAI,KAAK;AAAA,MACpC,cAAc;AAAA,MACd;AAAA,IACF,CAAC,EAAE,KAAK,IAAI,SAAO;AAGjB,aAAO,sBAAsB,GAAG;AAAA,IAClC,CAAC,GAAG,SAAS,MAAM,KAAK,sBAAsB,OAAO,GAAG,CAAC,GAAG,MAAM,CAAC;AACnE,SAAK,sBAAsB,IAAI,KAAK,GAAG;AACvC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,WAAW,UAAU,QAAQ;AAC7C,SAAK,gBAAgB,IAAI,QAAQ,WAAW,QAAQ,GAAG,MAAM;AAC7D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,WAAW,QAAQ;AACtC,UAAM,kBAAkB,KAAK,gBAAgB,IAAI,SAAS;AAC1D,QAAI,iBAAiB;AACnB,sBAAgB,KAAK,MAAM;AAAA,IAC7B,OAAO;AACL,WAAK,gBAAgB,IAAI,WAAW,CAAC,MAAM,CAAC;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,sBAAsB,QAAQ;AAC5B,QAAI,CAAC,OAAO,YAAY;AACtB,YAAM,MAAM,KAAK,sBAAsB,OAAO,OAAO;AACrD,WAAK,kBAAkB,KAAK,OAAO,OAAO;AAC1C,aAAO,aAAa;AAAA,IACtB;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA,EAEA,4BAA4B,WAAW,MAAM;AAC3C,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,YAAM,SAAS,KAAK,WAAW,CAAC,EAAE,MAAM,SAAS;AACjD,UAAI,QAAQ;AACV,eAAO,qBAAqB,MAAM,IAAI,IAAI,cAAc,OAAO,KAAK,MAAM,OAAO,OAAO,IAAI,IAAI,cAAc,QAAQ,IAAI;AAAA,MAC5H;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,SAAY,YAAY,CAAC,GAAM,SAAY,YAAY,GAAM,SAAS,UAAU,CAAC,GAAM,SAAY,YAAY,CAAC;AAAA,EACvK;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,SAAS,+BAA+B,gBAAgB,YAAY,WAAW,cAAc,UAAU;AACrG,SAAO,kBAAkB,IAAI,gBAAgB,YAAY,WAAW,UAAU,YAAY;AAC5F;AAMA,IAAM,yBAAyB;AAAA;AAAA,EAE7B,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,eAAe,GAAG,CAAC,IAAI,SAAS,GAAG,UAAU,GAAG,cAAc,cAAc,CAAC,IAAI,SAAS,GAAG,QAAQ,CAAC;AAAA,EAC9I,YAAY;AACd;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,IAAI,UAAU,IAAI;AAC3B;AAEA,SAAS,QAAQ,WAAW,MAAM;AAChC,SAAO,YAAY,MAAM;AAC3B;AACA,SAAS,qBAAqB,OAAO;AACnC,SAAO,CAAC,EAAE,MAAM,OAAO,MAAM;AAC/B;;;ACnoBA,IAAM,MAAM,CAAC,GAAG;AAShB,IAAM,2BAA2B,IAAI,eAAe,0BAA0B;AAM9E,IAAM,oBAAoB,IAAI,eAAe,qBAAqB;AAAA,EAChE,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,4BAA4B;AACnC,QAAM,YAAY,OAAO,QAAQ;AACjC,QAAM,YAAY,YAAY,UAAU,WAAW;AACnD,SAAO;AAAA;AAAA;AAAA,IAGL,aAAa,MAAM,YAAY,UAAU,WAAW,UAAU,SAAS;AAAA,EACzE;AACF;AAEA,IAAM,oBAAoB,CAAC,aAAa,iBAAiB,OAAO,UAAU,QAAQ,UAAU,UAAU,gBAAgB,cAAc,cAAc,QAAQ,QAAQ;AAElK,IAAM,2BAA2B,kBAAkB,IAAI,UAAQ,IAAI,IAAI,GAAG,EAAE,KAAK,IAAI;AAErF,IAAM,iBAAiB;AAiCvB,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,cAAc,OAAO,UAAU;AAAA,EAC/B,gBAAgB,OAAO,eAAe;AAAA,EACtC,YAAY,OAAO,iBAAiB;AAAA,EACpC,gBAAgB,OAAO,YAAY;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AAAA;AAAA,EAET,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,UAAU,KAAK,UAAU;AAC3B,UAAI,OAAO;AACT,aAAK,eAAe,KAAK;AAAA,MAC3B,WAAW,KAAK,UAAU;AACxB,aAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,UAAM,WAAW,KAAK,kBAAkB,KAAK;AAC7C,QAAI,aAAa,KAAK,UAAU;AAC9B,WAAK,WAAW;AAChB,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,UAAM,WAAW,KAAK,kBAAkB,KAAK;AAC7C,QAAI,aAAa,KAAK,WAAW;AAC/B,WAAK,YAAY;AACjB,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA;AAAA,EACA,wBAAwB,CAAC;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,oBAAoB,aAAa;AAAA,EACjC,cAAc;AACZ,UAAM,aAAa,OAAO,IAAI,mBAAmB,aAAa,GAAG;AAAA,MAC/D,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,0BAA0B;AAAA,MAChD,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,UAAU;AACZ,UAAI,SAAS,OAAO;AAClB,aAAK,QAAQ,KAAK,gBAAgB,SAAS;AAAA,MAC7C;AACA,UAAI,SAAS,SAAS;AACpB,aAAK,UAAU,SAAS;AAAA,MAC1B;AAAA,IACF;AAGA,QAAI,CAAC,YAAY;AACf,WAAK,YAAY,cAAc,aAAa,eAAe,MAAM;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,eAAe,UAAU;AACvB,QAAI,CAAC,UAAU;AACb,aAAO,CAAC,IAAI,EAAE;AAAA,IAChB;AACA,UAAM,QAAQ,SAAS,MAAM,GAAG;AAChC,YAAQ,MAAM,QAAQ;AAAA,MACpB,KAAK;AACH,eAAO,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA;AAAA,MAEtB,KAAK;AACH,eAAO;AAAA,MACT;AACE,cAAM,MAAM,uBAAuB,QAAQ,GAAG;AAAA,IAElD;AAAA,EACF;AAAA,EACA,WAAW;AAGT,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,qBAAqB;AACnB,UAAM,iBAAiB,KAAK;AAC5B,QAAI,kBAAkB,eAAe,MAAM;AACzC,YAAM,UAAU,KAAK,UAAU,YAAY;AAO3C,UAAI,YAAY,KAAK,eAAe;AAClC,aAAK,gBAAgB;AACrB,aAAK,yBAAyB,OAAO;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB,YAAY;AACnC,QAAI,KAAK,iCAAiC;AACxC,WAAK,gCAAgC,MAAM;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,WAAO,CAAC,KAAK;AAAA,EACf;AAAA,EACA,eAAe,KAAK;AAClB,SAAK,iBAAiB;AAGtB,UAAM,OAAO,KAAK,UAAU,YAAY;AACxC,SAAK,gBAAgB;AACrB,SAAK,qCAAqC,GAAG;AAC7C,SAAK,yBAAyB,IAAI;AAClC,SAAK,YAAY,cAAc,YAAY,GAAG;AAAA,EAChD;AAAA,EACA,mBAAmB;AACjB,UAAM,gBAAgB,KAAK,YAAY;AACvC,QAAI,aAAa,cAAc,WAAW;AAC1C,QAAI,KAAK,iCAAiC;AACxC,WAAK,gCAAgC,MAAM;AAAA,IAC7C;AAGA,WAAO,cAAc;AACnB,YAAM,QAAQ,cAAc,WAAW,UAAU;AAGjD,UAAI,MAAM,aAAa,KAAK,MAAM,SAAS,YAAY,MAAM,OAAO;AAClE,cAAM,OAAO;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,QAAI,CAAC,KAAK,eAAe,GAAG;AAC1B;AAAA,IACF;AACA,UAAM,OAAO,KAAK,YAAY;AAC9B,UAAM,kBAAkB,KAAK,UAAU,KAAK,cAAc,sBAAsB,KAAK,OAAO,EAAE,MAAM,IAAI,IAAI,KAAK,cAAc,uBAAuB,GAAG,OAAO,eAAa,UAAU,SAAS,CAAC;AACjM,SAAK,sBAAsB,QAAQ,eAAa,KAAK,UAAU,OAAO,SAAS,CAAC;AAChF,mBAAe,QAAQ,eAAa,KAAK,UAAU,IAAI,SAAS,CAAC;AACjE,SAAK,wBAAwB;AAC7B,QAAI,KAAK,aAAa,KAAK,0BAA0B,CAAC,eAAe,SAAS,mBAAmB,GAAG;AAClG,UAAI,KAAK,wBAAwB;AAC/B,aAAK,UAAU,OAAO,KAAK,sBAAsB;AAAA,MACnD;AACA,UAAI,KAAK,UAAU;AACjB,aAAK,UAAU,IAAI,KAAK,QAAQ;AAAA,MAClC;AACA,WAAK,yBAAyB,KAAK;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,OAAO;AACvB,WAAO,OAAO,UAAU,WAAW,MAAM,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,MAAM;AAC7B,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACZ,eAAS,QAAQ,CAAC,OAAO,YAAY;AACnC,cAAM,QAAQ,UAAQ;AACpB,kBAAQ,aAAa,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK,KAAK,IAAI;AAAA,QAChE,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qCAAqC,SAAS;AAC5C,UAAM,sBAAsB,QAAQ,iBAAiB,wBAAwB;AAC7E,UAAM,WAAW,KAAK,kCAAkC,KAAK,mCAAmC,oBAAI,IAAI;AACxG,aAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,wBAAkB,QAAQ,UAAQ;AAChC,cAAM,uBAAuB,oBAAoB,CAAC;AAClD,cAAM,QAAQ,qBAAqB,aAAa,IAAI;AACpD,cAAM,QAAQ,QAAQ,MAAM,MAAM,cAAc,IAAI;AACpD,YAAI,OAAO;AACT,cAAI,aAAa,SAAS,IAAI,oBAAoB;AAClD,cAAI,CAAC,YAAY;AACf,yBAAa,CAAC;AACd,qBAAS,IAAI,sBAAsB,UAAU;AAAA,UAC/C;AACA,qBAAW,KAAK;AAAA,YACd,MAAM;AAAA,YACN,OAAO,MAAM,CAAC;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,SAAS;AACtB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,kBAAkB,YAAY;AACnC,QAAI,SAAS;AACX,YAAM,CAAC,WAAW,QAAQ,IAAI,KAAK,eAAe,OAAO;AACzD,UAAI,WAAW;AACb,aAAK,gBAAgB;AAAA,MACvB;AACA,UAAI,UAAU;AACZ,aAAK,WAAW;AAAA,MAClB;AACA,WAAK,oBAAoB,KAAK,cAAc,gBAAgB,UAAU,SAAS,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,SAAO,KAAK,eAAe,GAAG,GAAG,SAAO;AAC/I,cAAM,eAAe,yBAAyB,SAAS,IAAI,QAAQ,KAAK,IAAI,OAAO;AACnF,aAAK,cAAc,YAAY,IAAI,MAAM,YAAY,CAAC;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,WAAW,CAAC,QAAQ,OAAO,GAAG,YAAY,aAAa;AAAA,IACvD,UAAU;AAAA,IACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,sBAAsB,IAAI,eAAe,IAAI,SAAS,KAAK,EAAE,sBAAsB,IAAI,YAAY,IAAI,QAAQ,EAAE,2BAA2B,IAAI,iBAAiB,IAAI,OAAO,EAAE,YAAY,IAAI,eAAe,IAAI,IAAI,WAAW,IAAI;AACnP,QAAG,WAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,YAAY,mBAAmB,IAAI,MAAM,EAAE,qBAAqB,IAAI,UAAU,aAAa,IAAI,UAAU,YAAY,IAAI,UAAU,MAAM;AAAA,MAC9I;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,QAAQ,CAAC,GAAG,UAAU,UAAU,gBAAgB;AAAA,MAChD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,+3BAA+3B;AAAA,IACx4B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,QACX,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,kCAAkC;AAAA,QAClC,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,6BAA6B;AAAA,MAC/B;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,+3BAA+3B;AAAA,IAC14B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,OAAO;AAAA,IAClC,SAAS,CAAC,SAAS,eAAe;AAAA,EACpC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,OAAO;AAAA,MAClC,SAAS,CAAC,SAAS,eAAe;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}