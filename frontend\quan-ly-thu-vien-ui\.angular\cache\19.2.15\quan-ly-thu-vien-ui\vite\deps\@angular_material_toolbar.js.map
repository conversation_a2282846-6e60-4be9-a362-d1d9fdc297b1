{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/toolbar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, inject, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport { Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nconst _c0 = [\"*\", [[\"mat-toolbar-row\"]]];\nconst _c1 = [\"*\", \"mat-toolbar-row\"];\nclass MatToolbarRow {\n  static ɵfac = function MatToolbarRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatToolbarRow)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatToolbarRow,\n    selectors: [[\"mat-toolbar-row\"]],\n    hostAttrs: [1, \"mat-toolbar-row\"],\n    exportAs: [\"matToolbarRow\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbarRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-toolbar-row',\n      exportAs: 'matToolbarRow',\n      host: {\n        'class': 'mat-toolbar-row'\n      }\n    }]\n  }], null, null);\n})();\nclass MatToolbar {\n  _elementRef = inject(ElementRef);\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT);\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the toolbar. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/toolbar/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Reference to all toolbar row elements that have been projected. */\n  _toolbarRows;\n  constructor() {}\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._checkToolbarMixedModes();\n      this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n    }\n  }\n  /**\n   * Throws an exception when developers are attempting to combine the different toolbar row modes.\n   */\n  _checkToolbarMixedModes() {\n    if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      // Check if there are any other DOM nodes that can display content but aren't inside of\n      // a <mat-toolbar-row> element.\n      const isCombinedUsage = Array.from(this._elementRef.nativeElement.childNodes).filter(node => !(node.classList && node.classList.contains('mat-toolbar-row'))).filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8)).some(node => !!(node.textContent && node.textContent.trim()));\n      if (isCombinedUsage) {\n        throwToolbarMixedModesError();\n      }\n    }\n  }\n  static ɵfac = function MatToolbar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatToolbar)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatToolbar,\n    selectors: [[\"mat-toolbar\"]],\n    contentQueries: function MatToolbar_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatToolbarRow, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._toolbarRows = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-toolbar\"],\n    hostVars: 6,\n    hostBindings: function MatToolbar_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-toolbar-multiple-rows\", ctx._toolbarRows.length > 0)(\"mat-toolbar-single-row\", ctx._toolbarRows.length === 0);\n      }\n    },\n    inputs: {\n      color: \"color\"\n    },\n    exportAs: [\"matToolbar\"],\n    ngContentSelectors: _c1,\n    decls: 2,\n    vars: 0,\n    template: function MatToolbar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵprojection(0);\n        i0.ɵɵprojection(1, 1);\n      }\n    },\n    styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbar, [{\n    type: Component,\n    args: [{\n      selector: 'mat-toolbar',\n      exportAs: 'matToolbar',\n      host: {\n        'class': 'mat-toolbar',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[class.mat-toolbar-multiple-rows]': '_toolbarRows.length > 0',\n        '[class.mat-toolbar-single-row]': '_toolbarRows.length === 0'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-toolbar-row\\\"></ng-content>\\n\",\n      styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _toolbarRows: [{\n      type: ContentChildren,\n      args: [MatToolbarRow, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nfunction throwToolbarMixedModesError() {\n  throw Error('MatToolbar: Attempting to combine different toolbar modes. ' + 'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' + 'inside of a `<mat-toolbar>` for a single row.');\n}\nclass MatToolbarModule {\n  static ɵfac = function MatToolbarModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatToolbarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatToolbarModule,\n    imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n    exports: [MatToolbar, MatToolbarRow, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n      exports: [MatToolbar, MatToolbarRow, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatToolbar, MatToolbarModule, MatToolbarRow, throwToolbarMixedModesError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACvC,IAAM,MAAM,CAAC,KAAK,iBAAiB;AACnC,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,WAAW,CAAC,GAAG,iBAAiB;AAAA,IAChC,UAAU,CAAC,eAAe;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc,OAAO,UAAU;AAAA,EAC/B,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3B;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,wBAAwB;AAC7B,WAAK,aAAa,QAAQ,UAAU,MAAM,KAAK,wBAAwB,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B;AACxB,QAAI,KAAK,aAAa,WAAW,OAAO,cAAc,eAAe,YAAY;AAG/E,YAAM,kBAAkB,MAAM,KAAK,KAAK,YAAY,cAAc,UAAU,EAAE,OAAO,UAAQ,EAAE,KAAK,aAAa,KAAK,UAAU,SAAS,iBAAiB,EAAE,EAAE,OAAO,UAAQ,KAAK,cAAc,KAAK,YAAY,KAAK,UAAU,eAAe,EAAE,EAAE,KAAK,UAAQ,CAAC,EAAE,KAAK,eAAe,KAAK,YAAY,KAAK,EAAE;AAC/S,UAAI,iBAAiB;AACnB,oCAA4B;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,gBAAgB,SAAS,0BAA0B,IAAI,KAAK,UAAU;AACpE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,aAAa;AAAA,IAC5B,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,YAAY,6BAA6B,IAAI,aAAa,SAAS,CAAC,EAAE,0BAA0B,IAAI,aAAa,WAAW,CAAC;AAAA,MAClI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,IACvB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,GAAG,CAAC;AAAA,MACtB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,ogEAAogE;AAAA,IAC7gE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,WAAW;AAAA,QACX,qCAAqC;AAAA,QACrC,kCAAkC;AAAA,MACpC;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,QAAQ,CAAC,ogEAAogE;AAAA,IAC/gE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,8BAA8B;AACrC,QAAM,MAAM,gMAA0M;AACxN;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,YAAY,aAAa;AAAA,IACpD,SAAS,CAAC,YAAY,eAAe,eAAe;AAAA,EACtD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,YAAY,aAAa;AAAA,MACpD,SAAS,CAAC,YAAY,eAAe,eAAe;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}