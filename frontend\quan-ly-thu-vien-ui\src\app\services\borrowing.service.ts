import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { 
  Borrowing, 
  CreateBorrowingRequest, 
  UpdateBorrowingRequest,
  ReturnBookRequest,
  BorrowingSearchRequest, 
  PagedResult,
  BorrowingStatistics,
  BorrowingValidation,
  QuickBorrowingInfo,
  BorrowingStatus
} from '../models/borrowing.model';

@Injectable({
  providedIn: 'root'
})
export class BorrowingService {
  private readonly API_URL = 'http://localhost:5247/api';

  constructor(private http: HttpClient) {}

  getBorrowings(searchRequest: BorrowingSearchRequest): Observable<PagedResult<Borrowing>> {
    let params = new HttpParams()
      .set('page', searchRequest.page.toString())
      .set('pageSize', searchRequest.pageSize.toString())
      .set('sortDescending', searchRequest.sortDescending.toString());

    if (searchRequest.nguoiDungId) {
      params = params.set('nguoiDungId', searchRequest.nguoiDungId.toString());
    }
    if (searchRequest.sachId) {
      params = params.set('sachId', searchRequest.sachId.toString());
    }
    if (searchRequest.trangThai) {
      params = params.set('trangThai', searchRequest.trangThai);
    }
    if (searchRequest.ngayMuonTu) {
      params = params.set('ngayMuonTu', searchRequest.ngayMuonTu);
    }
    if (searchRequest.ngayMuonDen) {
      params = params.set('ngayMuonDen', searchRequest.ngayMuonDen);
    }
    if (searchRequest.ngayHenTraTu) {
      params = params.set('ngayHenTraTu', searchRequest.ngayHenTraTu);
    }
    if (searchRequest.ngayHenTraDen) {
      params = params.set('ngayHenTraDen', searchRequest.ngayHenTraDen);
    }
    if (searchRequest.isOverdue !== undefined) {
      params = params.set('isOverdue', searchRequest.isOverdue.toString());
    }
    if (searchRequest.hoTenNguoiDung) {
      params = params.set('hoTenNguoiDung', searchRequest.hoTenNguoiDung);
    }
    if (searchRequest.tenSach) {
      params = params.set('tenSach', searchRequest.tenSach);
    }
    if (searchRequest.sortBy) {
      params = params.set('sortBy', searchRequest.sortBy);
    }

    return this.http.get<PagedResult<Borrowing>>(`${this.API_URL}/muontrasach`, { params })
      .pipe(catchError(this.handleError));
  }

  getBorrowingById(id: number): Observable<Borrowing> {
    return this.http.get<Borrowing>(`${this.API_URL}/muontrasach/${id}`)
      .pipe(catchError(this.handleError));
  }

  createBorrowing(borrowing: CreateBorrowingRequest): Observable<Borrowing> {
    return this.http.post<Borrowing>(`${this.API_URL}/muontrasach`, borrowing)
      .pipe(catchError(this.handleError));
  }

  updateBorrowing(id: number, borrowing: UpdateBorrowingRequest): Observable<Borrowing> {
    return this.http.put<Borrowing>(`${this.API_URL}/muontrasach/${id}`, borrowing)
      .pipe(catchError(this.handleError));
  }

  returnBook(id: number, returnData: ReturnBookRequest): Observable<Borrowing> {
    return this.http.post<Borrowing>(`${this.API_URL}/muontrasach/${id}/return`, returnData)
      .pipe(catchError(this.handleError));
  }

  deleteBorrowing(id: number): Observable<any> {
    return this.http.delete(`${this.API_URL}/muontrasach/${id}`)
      .pipe(catchError(this.handleError));
  }

  getUserBorrowings(userId: number): Observable<Borrowing[]> {
    return this.http.get<Borrowing[]>(`${this.API_URL}/muontrasach/user/${userId}`)
      .pipe(catchError(this.handleError));
  }

  getBookBorrowings(bookId: number): Observable<Borrowing[]> {
    return this.http.get<Borrowing[]>(`${this.API_URL}/muontrasach/book/${bookId}`)
      .pipe(catchError(this.handleError));
  }

  getOverdueBorrowings(): Observable<Borrowing[]> {
    return this.http.get<Borrowing[]>(`${this.API_URL}/muontrasach/overdue`)
      .pipe(catchError(this.handleError));
  }

  getActiveBorrowings(): Observable<Borrowing[]> {
    return this.http.get<Borrowing[]>(`${this.API_URL}/muontrasach/active`)
      .pipe(catchError(this.handleError));
  }

  getBorrowingStatistics(): Observable<BorrowingStatistics> {
    return this.http.get<BorrowingStatistics>(`${this.API_URL}/muontrasach/statistics`)
      .pipe(catchError(this.handleError));
  }

  validateBorrowing(userId: number, bookId: number): Observable<BorrowingValidation> {
    const params = new HttpParams()
      .set('userId', userId.toString())
      .set('bookId', bookId.toString());
    
    return this.http.get<BorrowingValidation>(`${this.API_URL}/muontrasach/validate`, { params })
      .pipe(catchError(this.handleError));
  }

  getQuickBorrowingInfo(): Observable<QuickBorrowingInfo> {
    return this.http.get<QuickBorrowingInfo>(`${this.API_URL}/muontrasach/quick-info`)
      .pipe(catchError(this.handleError));
  }

  extendBorrowing(id: number, newDueDate: string): Observable<Borrowing> {
    const data = { ngayHenTra: newDueDate };
    return this.http.post<Borrowing>(`${this.API_URL}/muontrasach/${id}/extend`, data)
      .pipe(catchError(this.handleError));
  }

  markAsLost(id: number, reason?: string): Observable<Borrowing> {
    const data = { ghiChu: reason };
    return this.http.post<Borrowing>(`${this.API_URL}/muontrasach/${id}/mark-lost`, data)
      .pipe(catchError(this.handleError));
  }

  sendOverdueReminder(id: number): Observable<any> {
    return this.http.post(`${this.API_URL}/muontrasach/${id}/send-reminder`, {})
      .pipe(catchError(this.handleError));
  }

  getBorrowingHistory(userId: number, limit: number = 10): Observable<Borrowing[]> {
    const params = new HttpParams()
      .set('userId', userId.toString())
      .set('limit', limit.toString());
    
    return this.http.get<Borrowing[]>(`${this.API_URL}/muontrasach/history`, { params })
      .pipe(catchError(this.handleError));
  }

  private handleError(error: any): Observable<never> {
    let errorMessage = 'Đã xảy ra lỗi không xác định';
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (typeof error.error === 'string') {
      errorMessage = error.error;
    }
    
    console.error('Borrowing Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
