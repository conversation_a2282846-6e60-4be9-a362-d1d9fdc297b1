using QuanLyThuVien.Application.DTOs;

namespace QuanLyThuVien.Application.Interfaces;

public interface IMuonTraSachService
{
    Task<PagedResult<MuonTraSachDetailDto>> GetAllAsync(MuonTraSachSearchDto searchDto);
    Task<MuonTraSachDetailDto?> GetByIdAsync(int id);
    Task<List<MuonTraSachDetailDto>> GetByNguoiDungIdAsync(int nguoiDungId);
    Task<List<MuonTraSachDetailDto>> GetActiveBorrowingsByNguoiDungIdAsync(int nguoiDungId);
    Task<List<MuonTraSachDetailDto>> GetOverdueBorrowingsAsync();
    Task<MuonTraSachDetailDto> MuonSachAsync(CreateMuonTraSachDto createDto);
    Task<MuonTraSachDetailDto> TraSachAsync(TraSachDto traSachDto);
    Task<MuonTraSachDetailDto> UpdateAsync(int id, UpdateMuonTraSachDto updateDto);
    Task<bool> DeleteAsync(int id);
    Task<bool> CanBorrowBookAsync(int nguoiDungId, int sachId);
    Task<ThongKeMuonTraDto> GetStatisticsAsync();
    Task<List<MuonTraSachDetailDto>> GetBorrowingHistoryAsync(int nguoiDungId, int page = 1, int pageSize = 10);
    Task<bool> UpdateOverdueStatusAsync();
    Task<decimal> CalculateFineAsync(int muonTraSachId);
}
