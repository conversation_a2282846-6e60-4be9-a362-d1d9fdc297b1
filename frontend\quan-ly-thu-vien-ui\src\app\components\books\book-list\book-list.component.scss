.book-list-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-card {
  .mat-mdc-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
  }

  .mat-mdc-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.search-form {
  .search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }

  .search-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}

.table-card {
  flex: 1;
}

.table-container {
  position: relative;
  min-height: 400px;
}

.book-table {
  width: 100%;

  .mat-mdc-header-cell {
    font-weight: 600;
    color: #333;
  }

  .mat-mdc-cell {
    padding: 12px 8px;
  }
}

.book-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .book-title {
    font-weight: 500;
    color: #333;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .book-isbn {
    font-size: 11px;
    color: #666;
    font-family: monospace;
  }
}

.action-buttons {
  display: flex;
  gap: 4px;
}

// Chip styles
.mat-mdc-chip {
  font-size: 11px;
  font-weight: 500;

  &.category-chip {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  &.availability-good {
    background-color: #4caf50;
    color: white;
  }

  &.availability-low {
    background-color: #ff9800;
    color: white;
  }

  &.availability-none {
    background-color: #f44336;
    color: white;
  }
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .book-list-container {
    padding: 16px;
  }

  .search-card .mat-mdc-card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .search-row {
    grid-template-columns: 1fr;
  }

  .search-actions {
    justify-content: stretch;
    
    button {
      flex: 1;
    }
  }

  .book-table {
    .mat-mdc-cell,
    .mat-mdc-header-cell {
      padding: 8px 4px;
      font-size: 12px;
    }
  }

  .book-info .book-title {
    max-width: 150px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
}

@media (max-width: 480px) {
  .book-list-container {
    padding: 12px;
  }

  .header-actions {
    width: 100%;
    
    button {
      width: 100%;
    }
  }

  .displayedColumns {
    // Hide some columns on mobile
    .mat-column-namXuatBan,
    .mat-column-gia {
      display: none;
    }
  }
}

// Custom snackbar styles
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

::ng-deep .info-snackbar {
  background-color: #2196f3 !important;
  color: white !important;
}

// Table hover effects
.book-table .mat-mdc-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

// Button hover effects
.action-buttons button:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

// Disabled button styles
.action-buttons button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

// Currency formatting
.currency {
  font-weight: 500;
  color: #2e7d32;
}

// Search form enhancements
.search-form {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      border-radius: 8px;
    }
  }
}

// Card enhancements
.search-card,
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// Animation for loading
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.book-table .mat-mdc-row {
  animation: fadeIn 0.3s ease-in;
}
