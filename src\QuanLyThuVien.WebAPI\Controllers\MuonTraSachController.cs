using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;

namespace QuanLyThuVien.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize] // All endpoints require authentication
public class MuonTraSachController : ControllerBase
{
    private readonly IMuonTraSachService _muonTraSachService;

    public MuonTraSachController(IMuonTraSachService muonTraSachService)
    {
        _muonTraSachService = muonTraSachService;
    }

    /// <summary>
    /// Lấy danh sách mượn trả sách với tìm kiếm và phân trang
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<MuonTraSachDetailDto>>> GetAll([FromQuery] MuonTraSachSearchDto searchDto)
    {
        try
        {
            var result = await _muonTraSachService.GetAllAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy thông tin mượn trả sách theo ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<MuonTraSachDetailDto>> GetById(int id)
    {
        try
        {
            var muonTraSach = await _muonTraSachService.GetByIdAsync(id);
            if (muonTraSach == null)
            {
                return NotFound(new { message = $"Không tìm thấy bản ghi mượn trả với ID: {id}" });
            }

            return Ok(muonTraSach);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy danh sách mượn trả theo người dùng
    /// </summary>
    [HttpGet("by-user/{nguoiDungId}")]
    public async Task<ActionResult<List<MuonTraSachDetailDto>>> GetByNguoiDungId(int nguoiDungId)
    {
        try
        {
            var result = await _muonTraSachService.GetByNguoiDungIdAsync(nguoiDungId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy danh sách sách đang mượn của người dùng
    /// </summary>
    [HttpGet("active-borrowings/{nguoiDungId}")]
    public async Task<ActionResult<List<MuonTraSachDetailDto>>> GetActiveBorrowingsByNguoiDungId(int nguoiDungId)
    {
        try
        {
            var result = await _muonTraSachService.GetActiveBorrowingsByNguoiDungIdAsync(nguoiDungId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy danh sách sách quá hạn
    /// </summary>
    [HttpGet("overdue")]
    public async Task<ActionResult<List<MuonTraSachDetailDto>>> GetOverdueBorrowings()
    {
        try
        {
            var result = await _muonTraSachService.GetOverdueBorrowingsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Mượn sách
    /// </summary>
    [HttpPost("muon")]
    public async Task<ActionResult<MuonTraSachDetailDto>> MuonSach([FromBody] CreateMuonTraSachDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _muonTraSachService.MuonSachAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Trả sách
    /// </summary>
    [HttpPost("tra")]
    public async Task<ActionResult<MuonTraSachDetailDto>> TraSach([FromBody] TraSachDto traSachDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _muonTraSachService.TraSachAsync(traSachDto);
            return Ok(result);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Cập nhật thông tin mượn sách
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<MuonTraSachDetailDto>> Update(int id, [FromBody] UpdateMuonTraSachDto updateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _muonTraSachService.UpdateAsync(id, updateDto);
            return Ok(result);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Xóa bản ghi mượn trả (soft delete)
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        try
        {
            var result = await _muonTraSachService.DeleteAsync(id);
            if (result)
            {
                return Ok(new { message = "Xóa bản ghi mượn trả thành công" });
            }
            return BadRequest(new { message = "Không thể xóa bản ghi mượn trả" });
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Kiểm tra xem người dùng có thể mượn sách không
    /// </summary>
    [HttpGet("can-borrow/{nguoiDungId}/{sachId}")]
    public async Task<ActionResult<bool>> CanBorrowBook(int nguoiDungId, int sachId)
    {
        try
        {
            var result = await _muonTraSachService.CanBorrowBookAsync(nguoiDungId, sachId);
            return Ok(new { canBorrow = result });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy thống kê mượn trả sách
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<ThongKeMuonTraDto>> GetStatistics()
    {
        try
        {
            var result = await _muonTraSachService.GetStatisticsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy lịch sử mượn sách của người dùng
    /// </summary>
    [HttpGet("history/{nguoiDungId}")]
    public async Task<ActionResult<List<MuonTraSachDetailDto>>> GetBorrowingHistory(
        int nguoiDungId, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var result = await _muonTraSachService.GetBorrowingHistoryAsync(nguoiDungId, page, pageSize);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Cập nhật trạng thái quá hạn
    /// </summary>
    [HttpPost("update-overdue-status")]
    public async Task<ActionResult> UpdateOverdueStatus()
    {
        try
        {
            var result = await _muonTraSachService.UpdateOverdueStatusAsync();
            return Ok(new { message = "Cập nhật trạng thái quá hạn thành công", updated = result });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Tính tiền phạt
    /// </summary>
    [HttpGet("calculate-fine/{muonTraSachId}")]
    public async Task<ActionResult<decimal>> CalculateFine(int muonTraSachId)
    {
        try
        {
            var fine = await _muonTraSachService.CalculateFineAsync(muonTraSachId);
            return Ok(new { fine });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }
}
