using Microsoft.AspNetCore.Mvc;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;

namespace QuanLyThuVien.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MuonTraSachController : ControllerBase
{
    private readonly IMuonTraSachService _muonTraSachService;

    public MuonTraSachController(IMuonTraSachService muonTraSachService)
    {
        _muonTraSachService = muonTraSachService;
    }

    /// <summary>
    /// Lấy danh sách mượn trả sách với tìm kiếm và phân trang
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<MuonTraSachDetailDto>>> GetAll([FromQuery] MuonTraSachSearchDto searchDto)
    {
        try
        {
            var result = await _muonTraSachService.GetAllAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy thông tin mượn trả sách theo ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<MuonTraSachDetailDto>> GetById(int id)
    {
        try
        {
            var muonTraSach = await _muonTraSachService.GetByIdAsync(id);
            if (muonTraSach == null)
            {
                return NotFound(new { message = $"Không tìm thấy bản ghi mượn trả sách với ID: {id}" });
            }
            return Ok(muonTraSach);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Tạo bản ghi mượn sách mới
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<MuonTraSachDetailDto>> Create([FromBody] CreateMuonTraSachDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var muonTraSach = await _muonTraSachService.MuonSachAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { id = muonTraSach.Id }, muonTraSach);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Trả sách
    /// </summary>
    [HttpPost("{id}/return")]
    public async Task<ActionResult<MuonTraSachDetailDto>> ReturnBook(int id, [FromBody] ReturnBookRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var traSachDto = new TraSachDto
            {
                MuonTraSachId = id,
                NgayTraThucTe = DateTime.Parse(request.NgayTraThucTe),
                GhiChu = request.GhiChu
            };

            var muonTraSach = await _muonTraSachService.TraSachAsync(traSachDto);
            return Ok(muonTraSach);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Gia hạn mượn sách (cập nhật thông tin)
    /// </summary>
    [HttpPost("{id}/extend")]
    public async Task<ActionResult<MuonTraSachDetailDto>> ExtendBorrowing(int id, [FromBody] ExtendBorrowingRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var updateDto = new UpdateMuonTraSachDto
            {
                NgayHenTra = DateTime.Parse(request.NgayHenTraMoi)
            };

            var muonTraSach = await _muonTraSachService.UpdateAsync(id, updateDto);
            return Ok(muonTraSach);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Xóa bản ghi mượn trả sách (soft delete)
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        try
        {
            var result = await _muonTraSachService.DeleteAsync(id);
            if (!result)
            {
                return NotFound(new { message = $"Không tìm thấy bản ghi mượn trả sách với ID: {id}" });
            }
            return Ok(new { message = "Xóa bản ghi mượn trả sách thành công" });
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }
}

public class ReturnBookRequest
{
    public string NgayTraThucTe { get; set; } = string.Empty;
    public string? GhiChu { get; set; }
}

public class ExtendBorrowingRequest
{
    public string NgayHenTraMoi { get; set; } = string.Empty;
}
