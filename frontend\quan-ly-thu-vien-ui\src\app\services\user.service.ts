import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { 
  User, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserSearchRequest, 
  PagedResult,
  UserStatistics 
} from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly API_URL = 'http://localhost:5247/api';

  constructor(private http: HttpClient) {}

  getUsers(searchRequest: UserSearchRequest): Observable<PagedResult<User>> {
    let params = new HttpParams()
      .set('page', searchRequest.page.toString())
      .set('pageSize', searchRequest.pageSize.toString())
      .set('sortDescending', searchRequest.sortDescending.toString());

    if (searchRequest.hoTen) {
      params = params.set('hoTen', searchRequest.hoTen);
    }
    if (searchRequest.email) {
      params = params.set('email', searchRequest.email);
    }
    if (searchRequest.soDienThoai) {
      params = params.set('soDienThoai', searchRequest.soDienThoai);
    }
    if (searchRequest.vaiTro) {
      params = params.set('vaiTro', searchRequest.vaiTro);
    }
    if (searchRequest.trangThaiHoatDong !== undefined) {
      params = params.set('trangThaiHoatDong', searchRequest.trangThaiHoatDong.toString());
    }
    if (searchRequest.ngayDangKyTu) {
      params = params.set('ngayDangKyTu', searchRequest.ngayDangKyTu);
    }
    if (searchRequest.ngayDangKyDen) {
      params = params.set('ngayDangKyDen', searchRequest.ngayDangKyDen);
    }
    if (searchRequest.sortBy) {
      params = params.set('sortBy', searchRequest.sortBy);
    }

    return this.http.get<PagedResult<User>>(`${this.API_URL}/nguoidung`, { params })
      .pipe(catchError(this.handleError));
  }

  getUserById(id: number): Observable<User> {
    return this.http.get<User>(`${this.API_URL}/nguoidung/${id}`)
      .pipe(catchError(this.handleError));
  }

  getUserByEmail(email: string): Observable<User> {
    return this.http.get<User>(`${this.API_URL}/nguoidung/by-email/${encodeURIComponent(email)}`)
      .pipe(catchError(this.handleError));
  }

  createUser(user: CreateUserRequest): Observable<User> {
    return this.http.post<User>(`${this.API_URL}/nguoidung`, user)
      .pipe(catchError(this.handleError));
  }

  updateUser(id: number, user: UpdateUserRequest): Observable<User> {
    return this.http.put<User>(`${this.API_URL}/nguoidung/${id}`, user)
      .pipe(catchError(this.handleError));
  }

  deleteUser(id: number): Observable<any> {
    return this.http.delete(`${this.API_URL}/nguoidung/${id}`)
      .pipe(catchError(this.handleError));
  }

  changeUserPassword(id: number, passwordData: { matKhauCu: string; matKhauMoi: string; xacNhanMatKhau: string }): Observable<any> {
    return this.http.post(`${this.API_URL}/nguoidung/${id}/change-password`, passwordData)
      .pipe(catchError(this.handleError));
  }

  getActiveUsers(): Observable<User[]> {
    return this.http.get<User[]>(`${this.API_URL}/nguoidung/active`)
      .pipe(catchError(this.handleError));
  }

  getUserStatistics(): Observable<UserStatistics> {
    return this.http.get<UserStatistics>(`${this.API_URL}/nguoidung/statistics`)
      .pipe(catchError(this.handleError));
  }

  private handleError(error: any): Observable<never> {
    let errorMessage = 'Đã xảy ra lỗi không xác định';
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (typeof error.error === 'string') {
      errorMessage = error.error;
    }
    
    console.error('User Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
