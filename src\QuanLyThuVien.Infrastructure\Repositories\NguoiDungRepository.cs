using Microsoft.EntityFrameworkCore;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Domain.Entities;
using QuanLyThuVien.Infrastructure.Data;

namespace QuanLyThuVien.Infrastructure.Repositories;

public class NguoiDungRepository : INguoiDungRepository
{
    private readonly QuanLyThuVienDbContext _context;

    public NguoiDungRepository(QuanLyThuVienDbContext context)
    {
        _context = context;
    }

    public async Task<PagedResult<NguoiDung>> GetAllAsync(NguoiDungSearchDto searchDto)
    {
        var query = _context.NguoiDung.AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(searchDto.HoTen))
        {
            query = query.Where(n => n.HoTen.Contains(searchDto.HoTen));
        }

        if (!string.IsNullOrEmpty(searchDto.Email))
        {
            query = query.Where(n => n.Email.Contains(searchDto.Email));
        }

        if (!string.IsNullOrEmpty(searchDto.SoDienThoai))
        {
            query = query.Where(n => n.SoDienThoai != null && n.SoDienThoai.Contains(searchDto.SoDienThoai));
        }

        if (!string.IsNullOrEmpty(searchDto.VaiTro))
        {
            query = query.Where(n => n.VaiTro == searchDto.VaiTro);
        }

        if (searchDto.TrangThaiHoatDong.HasValue)
        {
            query = query.Where(n => n.TrangThaiHoatDong == searchDto.TrangThaiHoatDong.Value);
        }

        if (searchDto.NgayDangKyTu.HasValue)
        {
            query = query.Where(n => n.NgayDangKy >= searchDto.NgayDangKyTu.Value);
        }

        if (searchDto.NgayDangKyDen.HasValue)
        {
            query = query.Where(n => n.NgayDangKy <= searchDto.NgayDangKyDen.Value);
        }

        // Apply sorting
        query = searchDto.SortBy?.ToLower() switch
        {
            "hoten" => searchDto.SortDescending ? query.OrderByDescending(n => n.HoTen) : query.OrderBy(n => n.HoTen),
            "email" => searchDto.SortDescending ? query.OrderByDescending(n => n.Email) : query.OrderBy(n => n.Email),
            "ngaydangky" => searchDto.SortDescending ? query.OrderByDescending(n => n.NgayDangKy) : query.OrderBy(n => n.NgayDangKy),
            "vaitro" => searchDto.SortDescending ? query.OrderByDescending(n => n.VaiTro) : query.OrderBy(n => n.VaiTro),
            "trangthai" => searchDto.SortDescending ? query.OrderByDescending(n => n.TrangThaiHoatDong) : query.OrderBy(n => n.TrangThaiHoatDong),
            _ => query.OrderBy(n => n.HoTen)
        };

        var totalCount = await query.CountAsync();

        var items = await query
            .Skip((searchDto.Page - 1) * searchDto.PageSize)
            .Take(searchDto.PageSize)
            .ToListAsync();

        return new PagedResult<NguoiDung>
        {
            Items = items,
            TotalCount = totalCount,
            Page = searchDto.Page,
            PageSize = searchDto.PageSize
        };
    }

    public async Task<NguoiDung?> GetByIdAsync(int id)
    {
        return await _context.NguoiDung.FindAsync(id);
    }

    public async Task<NguoiDung?> GetByEmailAsync(string email)
    {
        return await _context.NguoiDung.FirstOrDefaultAsync(n => n.Email == email);
    }

    public async Task<NguoiDung?> GetBySoCanCuocAsync(string soCanCuoc)
    {
        return await _context.NguoiDung.FirstOrDefaultAsync(n => n.SoCanCuoc == soCanCuoc);
    }

    public async Task<NguoiDung> CreateAsync(NguoiDung nguoiDung)
    {
        _context.NguoiDung.Add(nguoiDung);
        await _context.SaveChangesAsync();
        return nguoiDung;
    }

    public async Task<NguoiDung> UpdateAsync(NguoiDung nguoiDung)
    {
        _context.Entry(nguoiDung).State = EntityState.Modified;
        await _context.SaveChangesAsync();
        return nguoiDung;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var nguoiDung = await _context.NguoiDung.FindAsync(id);
        if (nguoiDung == null) return false;

        nguoiDung.DaXoa = true;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ExistsAsync(int id)
    {
        return await _context.NguoiDung.AnyAsync(n => n.Id == id);
    }

    public async Task<bool> IsEmailExistsAsync(string email, int? excludeId = null)
    {
        var query = _context.NguoiDung.Where(n => n.Email == email);
        
        if (excludeId.HasValue)
        {
            query = query.Where(n => n.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    public async Task<bool> IsSoCanCuocExistsAsync(string soCanCuoc, int? excludeId = null)
    {
        var query = _context.NguoiDung.Where(n => n.SoCanCuoc == soCanCuoc);
        
        if (excludeId.HasValue)
        {
            query = query.Where(n => n.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    public async Task<bool> CanDeleteAsync(int id)
    {
        // Check if user has any active borrowing records
        var hasActiveBorrowings = await _context.MuonTraSach
            .AnyAsync(m => m.NguoiDungId == id && m.TrangThai == "DangMuon" && !m.DaXoa);

        return !hasActiveBorrowings;
    }

    public async Task<List<NguoiDung>> GetActiveUsersAsync()
    {
        return await _context.NguoiDung
            .Where(n => n.TrangThaiHoatDong)
            .OrderBy(n => n.HoTen)
            .ToListAsync();
    }

    public async Task<int> GetTotalUsersCountAsync()
    {
        return await _context.NguoiDung.CountAsync();
    }

    public async Task<int> GetActiveUsersCountAsync()
    {
        return await _context.NguoiDung.CountAsync(n => n.TrangThaiHoatDong);
    }
}
