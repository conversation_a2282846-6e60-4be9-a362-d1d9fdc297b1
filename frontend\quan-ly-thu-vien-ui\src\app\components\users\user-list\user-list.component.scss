.user-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-card {
  .mat-mdc-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
  }

  .mat-mdc-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.search-form {
  .search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }

  .search-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}

.table-card {
  flex: 1;
}

.table-container {
  position: relative;
  min-height: 400px;
}

.user-table {
  width: 100%;

  .mat-mdc-header-cell {
    font-weight: 600;
    color: #333;
  }

  .mat-mdc-cell {
    padding: 12px 8px;
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .user-name {
    font-weight: 500;
    color: #333;
  }

  .user-email {
    font-size: 12px;
    color: #666;
  }
}

.action-buttons {
  display: flex;
  gap: 4px;
}

// Chip styles
.mat-mdc-chip {
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.role-admin {
    background-color: #f44336;
    color: white;
  }

  &.role-librarian {
    background-color: #ff9800;
    color: white;
  }

  &.role-user {
    background-color: #4caf50;
    color: white;
  }

  &.status-active {
    background-color: #4caf50;
    color: white;
  }

  &.status-inactive {
    background-color: #9e9e9e;
    color: white;
  }
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .user-list-container {
    padding: 16px;
  }

  .search-card .mat-mdc-card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .search-row {
    grid-template-columns: 1fr;
  }

  .search-actions {
    justify-content: stretch;
    
    button {
      flex: 1;
    }
  }

  .user-table {
    .mat-mdc-cell,
    .mat-mdc-header-cell {
      padding: 8px 4px;
      font-size: 12px;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
}

@media (max-width: 480px) {
  .user-list-container {
    padding: 12px;
  }

  .header-actions {
    width: 100%;
    
    button {
      width: 100%;
    }
  }
}

// Custom snackbar styles
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

// Table hover effects
.user-table .mat-mdc-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

// Button hover effects
.action-buttons button:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}
