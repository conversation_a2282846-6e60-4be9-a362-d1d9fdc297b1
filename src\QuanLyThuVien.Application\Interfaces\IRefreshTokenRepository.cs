using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Interfaces;

public interface IRefreshTokenRepository
{
    Task<RefreshToken?> GetByTokenAsync(string token);
    Task<List<RefreshToken>> GetActiveTokensByUserIdAsync(int userId);
    Task<RefreshToken> CreateAsync(RefreshToken refreshToken);
    Task<RefreshToken> UpdateAsync(RefreshToken refreshToken);
    Task<bool> DeleteAsync(int id);
    Task<bool> RevokeTokenAsync(string token, string revokedByIp);
    Task<bool> RevokeAllUserTokensAsync(int userId, string revokedByIp);
    Task<bool> CleanupExpiredTokensAsync();
}
