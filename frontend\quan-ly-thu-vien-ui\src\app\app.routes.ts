import { Routes } from '@angular/router';
import { AuthGuard, RoleGuard, GuestGuard } from './guards/auth.guard';

export const routes: Routes = [
  // Public routes
  {
    path: 'login',
    loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent),
    canActivate: [GuestGuard]
  },
  {
    path: 'register',
    loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent),
    canActivate: [GuestGuard]
  },

  // Protected routes
  {
    path: 'dashboard',
    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),
    canActivate: [AuthGuard]
  },

  // TODO: Book management routes (will be implemented next)
  // {
  //   path: 'books',
  //   loadComponent: () => import('./components/books/book-list/book-list.component').then(m => m.BookListComponent),
  //   canActivate: [AuthGuard]
  // },

  // TODO: User management routes (will be implemented next)
  // {
  //   path: 'users',
  //   loadComponent: () => import('./components/users/user-list/user-list.component').then(m => m.UserListComponent),
  //   canActivate: [RoleGuard],
  //   data: { roles: ['Admin', 'Librarian'] }
  // },

  // TODO: Borrowing management routes (will be implemented next)
  // {
  //   path: 'borrowings',
  //   loadComponent: () => import('./components/borrowings/borrowing-list/borrowing-list.component').then(m => m.BorrowingListComponent),
  //   canActivate: [AuthGuard]
  // },

  // Profile routes
  {
    path: 'profile',
    loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent),
    canActivate: [AuthGuard]
  },

  // Error routes
  {
    path: 'unauthorized',
    loadComponent: () => import('./components/error/unauthorized/unauthorized.component').then(m => m.UnauthorizedComponent)
  },
  {
    path: 'not-found',
    loadComponent: () => import('./components/error/not-found/not-found.component').then(m => m.NotFoundComponent)
  },

  // Default redirects
  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
  { path: '**', redirectTo: '/not-found' }
];
