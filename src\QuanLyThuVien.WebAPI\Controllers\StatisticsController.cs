using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;

namespace QuanLyThuVien.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class StatisticsController : ControllerBase
{
    private readonly IStatisticsService _statisticsService;

    public StatisticsController(IStatisticsService statisticsService)
    {
        _statisticsService = statisticsService;
    }

    /// <summary>
    /// Lấy thống kê dashboard
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<DashboardStatisticsDto>> GetDashboardStatistics()
    {
        try
        {
            var statistics = await _statisticsService.GetDashboardStatisticsAsync();
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// L<PERSON>y danh sách sách được mượn nhiều nhất
    /// </summary>
    [HttpGet("top-books")]
    public async Task<ActionResult<List<TopBookDto>>> GetTopBooks([FromQuery] int count = 10)
    {
        try
        {
            var topBooks = await _statisticsService.GetTopBooksAsync(count);
            return Ok(topBooks);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy danh sách người mượn sách nhiều nhất
    /// </summary>
    [HttpGet("top-borrowers")]
    public async Task<ActionResult<List<TopBorrowerDto>>> GetTopBorrowers([FromQuery] int count = 10)
    {
        try
        {
            var topBorrowers = await _statisticsService.GetTopBorrowersAsync(count);
            return Ok(topBorrowers);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy danh sách hoạt động gần đây
    /// </summary>
    [HttpGet("recent-activities")]
    public async Task<ActionResult<List<RecentActivityDto>>> GetRecentActivities([FromQuery] int count = 20)
    {
        try
        {
            var activities = await _statisticsService.GetRecentActivitiesAsync(count);
            return Ok(activities);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }
}
