import { Compo<PERSON>, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSortModule, MatSort, Sort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { Mat<PERSON>nackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { BookService } from '../../../services/book.service';
import { AuthService } from '../../../services/auth.service';
import { Book, BookSearchRequest, BOOK_CATEGORIES, PUBLISHERS, SORT_OPTIONS } from '../../../models/book.model';

@Component({
  selector: 'app-book-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatTooltipModule
  ],
  template: `
    <div class="book-list-container">
      <mat-card class="search-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>book</mat-icon>
            Quản lý sách
          </mat-card-title>
          <div class="header-actions">
            <button mat-raised-button color="primary" routerLink="/books/add" *ngIf="canManageBooks()">
              <mat-icon>add</mat-icon>
              Thêm sách mới
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="searchForm" class="search-form">
            <div class="search-row">
              <mat-form-field appearance="outline">
                <mat-label>Tên sách</mat-label>
                <input matInput formControlName="tenSach" placeholder="Tìm theo tên sách">
                <mat-icon matSuffix>book</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Tác giả</mat-label>
                <input matInput formControlName="tacGia" placeholder="Tìm theo tác giả">
                <mat-icon matSuffix>person</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Thể loại</mat-label>
                <mat-select formControlName="theLoai">
                  <mat-option value="">Tất cả</mat-option>
                  <mat-option *ngFor="let category of bookCategories" [value]="category.value">
                    {{ category.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Nhà xuất bản</mat-label>
                <mat-select formControlName="nhaXuatBan">
                  <mat-option value="">Tất cả</mat-option>
                  <mat-option *ngFor="let publisher of publishers" [value]="publisher.value">
                    {{ publisher.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="search-row">
              <mat-form-field appearance="outline">
                <mat-label>ISBN</mat-label>
                <input matInput formControlName="isbn" placeholder="Tìm theo ISBN">
                <mat-icon matSuffix>qr_code</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Năm xuất bản từ</mat-label>
                <input matInput type="number" formControlName="namXuatBanTu" placeholder="2000">
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Năm xuất bản đến</mat-label>
                <input matInput type="number" formControlName="namXuatBanDen" placeholder="2024">
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Sắp xếp theo</mat-label>
                <mat-select formControlName="sortBy">
                  <mat-option *ngFor="let option of sortOptions" [value]="option.value">
                    {{ option.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="search-actions">
              <button mat-raised-button color="primary" (click)="onSearch()" [disabled]="isLoading">
                <mat-icon>search</mat-icon>
                Tìm kiếm
              </button>
              <button mat-button (click)="onReset()" [disabled]="isLoading">
                <mat-icon>refresh</mat-icon>
                Đặt lại
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <mat-card class="table-card">
        <mat-card-content>
          <div class="table-container">
            <mat-table [dataSource]="dataSource" matSort class="book-table">
              <ng-container matColumnDef="tenSach">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Tên sách</mat-header-cell>
                <mat-cell *matCellDef="let book">
                  <div class="book-info">
                    <span class="book-title">{{ book.tenSach }}</span>
                    <span class="book-isbn">ISBN: {{ book.isbn }}</span>
                  </div>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="tacGia">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Tác giả</mat-header-cell>
                <mat-cell *matCellDef="let book">{{ book.tacGia }}</mat-cell>
              </ng-container>

              <ng-container matColumnDef="theLoai">
                <mat-header-cell *matHeaderCellDef>Thể loại</mat-header-cell>
                <mat-cell *matCellDef="let book">
                  <mat-chip class="category-chip">{{ book.theLoai }}</mat-chip>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="namXuatBan">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Năm XB</mat-header-cell>
                <mat-cell *matCellDef="let book">{{ book.namXuatBan }}</mat-cell>
              </ng-container>

              <ng-container matColumnDef="soLuongConLai">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Còn lại</mat-header-cell>
                <mat-cell *matCellDef="let book">
                  <mat-chip [ngClass]="getAvailabilityClass(book.soLuongConLai)">
                    {{ book.soLuongConLai }}/{{ book.soLuongTong }}
                  </mat-chip>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="gia">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Giá</mat-header-cell>
                <mat-cell *matCellDef="let book">{{ formatCurrency(book.gia) }}</mat-cell>
              </ng-container>

              <ng-container matColumnDef="actions">
                <mat-header-cell *matHeaderCellDef>Thao tác</mat-header-cell>
                <mat-cell *matCellDef="let book">
                  <div class="action-buttons">
                    <button mat-icon-button color="primary" 
                            [routerLink]="['/books/edit', book.id]"
                            matTooltip="Chỉnh sửa"
                            *ngIf="canManageBooks()">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" 
                            (click)="onDelete(book)"
                            matTooltip="Xóa"
                            *ngIf="canManageBooks()">
                      <mat-icon>delete</mat-icon>
                    </button>
                    <button mat-icon-button color="accent" 
                            (click)="onBorrow(book)"
                            matTooltip="Mượn sách"
                            [disabled]="book.soLuongConLai === 0">
                      <mat-icon>assignment_add</mat-icon>
                    </button>
                  </div>
                </mat-cell>
              </ng-container>

              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </mat-table>

            <div class="loading-container" *ngIf="isLoading">
              <mat-spinner diameter="50"></mat-spinner>
            </div>

            <div class="no-data" *ngIf="!isLoading && dataSource.data.length === 0">
              <mat-icon>book_outline</mat-icon>
              <p>Không tìm thấy sách nào</p>
            </div>
          </div>

          <mat-paginator #paginator
                         [length]="totalCount"
                         [pageSize]="pageSize"
                         [pageSizeOptions]="[5, 10, 25, 50]"
                         [showFirstLastButtons]="true"
                         (page)="onPageChange($event)">
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styleUrls: ['./book-list.component.scss']
})
export class BookListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  searchForm!: FormGroup;
  dataSource = new MatTableDataSource<Book>([]);
  displayedColumns: string[] = ['tenSach', 'tacGia', 'theLoai', 'namXuatBan', 'soLuongConLai', 'gia', 'actions'];
  
  isLoading = false;
  totalCount = 0;
  pageSize = 10;
  currentPage = 1;
  
  bookCategories = BOOK_CATEGORIES;
  publishers = PUBLISHERS;
  sortOptions = SORT_OPTIONS;

  constructor(
    private bookService: BookService,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadBooks();
  }

  private initializeForm(): void {
    this.searchForm = this.formBuilder.group({
      tenSach: [''],
      tacGia: [''],
      theLoai: [''],
      nhaXuatBan: [''],
      isbn: [''],
      namXuatBanTu: [''],
      namXuatBanDen: [''],
      sortBy: ['tenSach']
    });
  }

  loadBooks(): void {
    this.isLoading = true;
    
    const searchRequest: BookSearchRequest = {
      ...this.searchForm.value,
      page: this.currentPage,
      pageSize: this.pageSize,
      sortBy: this.searchForm.value.sortBy || 'tenSach',
      sortDescending: this.sort?.direction === 'desc'
    };

    this.bookService.getBooks(searchRequest).subscribe({
      next: (result) => {
        this.dataSource.data = result.items;
        this.totalCount = result.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi tải danh sách sách', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSearch(): void {
    this.currentPage = 1;
    this.loadBooks();
  }

  onReset(): void {
    this.searchForm.reset();
    this.searchForm.patchValue({ sortBy: 'tenSach' });
    this.currentPage = 1;
    this.loadBooks();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadBooks();
  }

  onSortChange(sort: Sort): void {
    this.loadBooks();
  }

  onDelete(book: Book): void {
    if (confirm(`Bạn có chắc chắn muốn xóa sách "${book.tenSach}"?`)) {
      this.bookService.deleteBook(book.id).subscribe({
        next: () => {
          this.snackBar.open('Xóa sách thành công', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadBooks();
        },
        error: (error) => {
          this.snackBar.open(error.message || 'Lỗi khi xóa sách', 'Đóng', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  onBorrow(book: Book): void {
    // TODO: Implement borrow functionality
    this.snackBar.open('Chức năng mượn sách sẽ được phát triển trong phiên bản tiếp theo', 'Đóng', {
      duration: 3000,
      panelClass: ['info-snackbar']
    });
  }

  canManageBooks(): boolean {
    return this.authService.canManageBooks();
  }

  getAvailabilityClass(available: number): string {
    if (available === 0) return 'availability-none';
    if (available <= 2) return 'availability-low';
    return 'availability-good';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  }
}
