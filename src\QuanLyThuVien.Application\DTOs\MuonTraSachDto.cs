using System.ComponentModel.DataAnnotations;

namespace QuanLyThuVien.Application.DTOs;

public class MuonTraSachDto
{
    public int Id { get; set; }
    public int NguoiDungId { get; set; }
    public int SachId { get; set; }
    public DateTime NgayMuon { get; set; }
    public DateTime NgayHenTra { get; set; }
    public DateTime? NgayTraThucTe { get; set; }
    public string TrangThai { get; set; } = string.Empty;
    public string? GhiChu { get; set; }
    public decimal? TienPhat { get; set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }

    // Navigation properties
    public NguoiDungDto? NguoiDung { get; set; }
    public SachDto? Sach { get; set; }
}

public class CreateMuonTraSachDto
{
    [Required(ErrorMessage = "Người dùng là bắt buộc")]
    public int NguoiDungId { get; set; }

    [Required(ErrorMessage = "Sách là bắt buộc")]
    public int SachId { get; set; }

    [Required(ErrorMessage = "Ngày hẹn trả là bắt buộc")]
    public DateTime NgayHenTra { get; set; }

    [MaxLength(500, ErrorMessage = "Ghi chú không được vượt quá 500 ký tự")]
    public string? GhiChu { get; set; }
}

public class UpdateMuonTraSachDto
{
    [Required(ErrorMessage = "Ngày hẹn trả là bắt buộc")]
    public DateTime NgayHenTra { get; set; }

    [MaxLength(500, ErrorMessage = "Ghi chú không được vượt quá 500 ký tự")]
    public string? GhiChu { get; set; }
}

public class TraSachDto
{
    [Required(ErrorMessage = "ID mượn trả là bắt buộc")]
    public int MuonTraSachId { get; set; }

    public DateTime NgayTraThucTe { get; set; } = DateTime.Now;

    [MaxLength(500, ErrorMessage = "Ghi chú không được vượt quá 500 ký tự")]
    public string? GhiChu { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Tiền phạt phải >= 0")]
    public decimal? TienPhat { get; set; }
}

public class MuonTraSachSearchDto
{
    public int? NguoiDungId { get; set; }
    public int? SachId { get; set; }
    public string? TrangThai { get; set; }
    public DateTime? NgayMuonTu { get; set; }
    public DateTime? NgayMuonDen { get; set; }
    public DateTime? NgayHenTraTu { get; set; }
    public DateTime? NgayHenTraDen { get; set; }
    public bool? QuaHan { get; set; }
    public string? TenNguoiDung { get; set; }
    public string? TenSach { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; } = "NgayMuon";
    public bool SortDescending { get; set; } = true;
}

public class ThongKeMuonTraDto
{
    public int TongSoLuotMuon { get; set; }
    public int SoSachDangMuon { get; set; }
    public int SoSachDaTra { get; set; }
    public int SoSachQuaHan { get; set; }
    public decimal TongTienPhat { get; set; }
}

public class MuonTraSachDetailDto : MuonTraSachDto
{
    public string TenNguoiDung { get; set; } = string.Empty;
    public string EmailNguoiDung { get; set; } = string.Empty;
    public string SoDienThoaiNguoiDung { get; set; } = string.Empty;
    public string TenSach { get; set; } = string.Empty;
    public string TacGia { get; set; } = string.Empty;
    public string ISBN { get; set; } = string.Empty;
    public int SoNgayMuon => NgayTraThucTe.HasValue 
        ? (NgayTraThucTe.Value - NgayMuon).Days 
        : (DateTime.Now - NgayMuon).Days;
    public int SoNgayQuaHan => NgayTraThucTe.HasValue
        ? Math.Max(0, (NgayTraThucTe.Value - NgayHenTra).Days)
        : Math.Max(0, (DateTime.Now - NgayHenTra).Days);
    public bool LaQuaHan => SoNgayQuaHan > 0;
}
