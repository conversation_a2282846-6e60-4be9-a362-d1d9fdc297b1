using Microsoft.EntityFrameworkCore;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Domain.Entities;
using QuanLyThuVien.Infrastructure.Data;

namespace QuanLyThuVien.Infrastructure.Repositories;

public class SachRepository : ISachRepository
{
    private readonly QuanLyThuVienDbContext _context;

    public SachRepository(QuanLyThuVienDbContext context)
    {
        _context = context;
    }

    public async Task<PagedResult<Sach>> GetAllAsync(SachSearchDto searchDto)
    {
        var query = _context.Sach.AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(searchDto.TenSach))
        {
            query = query.Where(s => s.TenSach.Contains(searchDto.TenSach));
        }

        if (!string.IsNullOrEmpty(searchDto.TacGia))
        {
            query = query.Where(s => s.TacGia.Contains(searchDto.TacGia));
        }

        if (!string.IsNullOrEmpty(searchDto.TheLoai))
        {
            query = query.Where(s => s.TheLoai.Contains(searchDto.TheLoai));
        }

        if (searchDto.NamXuatBanTu.HasValue)
        {
            query = query.Where(s => s.NamXuatBan >= searchDto.NamXuatBanTu.Value);
        }

        if (searchDto.NamXuatBanDen.HasValue)
        {
            query = query.Where(s => s.NamXuatBan <= searchDto.NamXuatBanDen.Value);
        }

        // Apply sorting
        query = searchDto.SortBy?.ToLower() switch
        {
            "tensach" => searchDto.SortDescending ? query.OrderByDescending(s => s.TenSach) : query.OrderBy(s => s.TenSach),
            "tacgia" => searchDto.SortDescending ? query.OrderByDescending(s => s.TacGia) : query.OrderBy(s => s.TacGia),
            "namxuatban" => searchDto.SortDescending ? query.OrderByDescending(s => s.NamXuatBan) : query.OrderBy(s => s.NamXuatBan),
            "ngaytao" => searchDto.SortDescending ? query.OrderByDescending(s => s.NgayTao) : query.OrderBy(s => s.NgayTao),
            _ => query.OrderBy(s => s.TenSach)
        };

        var totalCount = await query.CountAsync();

        var items = await query
            .Skip((searchDto.Page - 1) * searchDto.PageSize)
            .Take(searchDto.PageSize)
            .ToListAsync();

        return new PagedResult<Sach>
        {
            Items = items,
            TotalCount = totalCount,
            Page = searchDto.Page,
            PageSize = searchDto.PageSize
        };
    }

    public async Task<Sach?> GetByIdAsync(int id)
    {
        return await _context.Sach.FindAsync(id);
    }

    public async Task<Sach> CreateAsync(Sach sach)
    {
        sach.SoLuongConLai = sach.SoLuongTong;
        _context.Sach.Add(sach);
        await _context.SaveChangesAsync();
        return sach;
    }

    public async Task<Sach> UpdateAsync(Sach sach)
    {
        _context.Entry(sach).State = EntityState.Modified;
        await _context.SaveChangesAsync();
        return sach;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var sach = await _context.Sach.FindAsync(id);
        if (sach == null) return false;

        sach.DaXoa = true;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ExistsAsync(int id)
    {
        return await _context.Sach.AnyAsync(s => s.Id == id);
    }

    public async Task<bool> IsISBNExistsAsync(string isbn, int? excludeId = null)
    {
        var query = _context.Sach.Where(s => s.ISBN == isbn);
        
        if (excludeId.HasValue)
        {
            query = query.Where(s => s.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    public async Task<int> GetTotalBooksCountAsync()
    {
        return await _context.Sach
            .Where(s => !s.DaXoa)
            .CountAsync();
    }
}
