using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Interfaces;

public interface ISachRepository
{
    Task<PagedResult<Sach>> GetAllAsync(SachSearchDto searchDto);
    Task<Sach?> GetByIdAsync(int id);
    Task<Sach> CreateAsync(Sach sach);
    Task<Sach> UpdateAsync(Sach sach);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
    Task<bool> IsISBNExistsAsync(string isbn, int? excludeId = null);
    Task<int> GetTotalBooksCountAsync();
}
