using Microsoft.EntityFrameworkCore;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Domain.Entities;
using QuanLyThuVien.Infrastructure.Data;

namespace QuanLyThuVien.Infrastructure.Repositories;

public class RefreshTokenRepository : IRefreshTokenRepository
{
    private readonly QuanLyThuVienDbContext _context;

    public RefreshTokenRepository(QuanLyThuVienDbContext context)
    {
        _context = context;
    }

    public async Task<RefreshToken?> GetByTokenAsync(string token)
    {
        return await _context.RefreshTokens
            .Include(rt => rt.NguoiDung)
            .FirstOrDefaultAsync(rt => rt.Token == token);
    }

    public async Task<List<RefreshToken>> GetActiveTokensByUserIdAsync(int userId)
    {
        return await _context.RefreshTokens
            .Where(rt => rt.NguoiDungId == userId && rt.IsActive)
            .OrderByDescending(rt => rt.NgayTao)
            .ToListAsync();
    }

    public async Task<RefreshToken> CreateAsync(RefreshToken refreshToken)
    {
        _context.RefreshTokens.Add(refreshToken);
        await _context.SaveChangesAsync();
        return refreshToken;
    }

    public async Task<RefreshToken> UpdateAsync(RefreshToken refreshToken)
    {
        _context.Entry(refreshToken).State = EntityState.Modified;
        await _context.SaveChangesAsync();
        return refreshToken;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var refreshToken = await _context.RefreshTokens.FindAsync(id);
        if (refreshToken == null) return false;

        refreshToken.DaXoa = true;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> RevokeTokenAsync(string token, string revokedByIp)
    {
        var refreshToken = await _context.RefreshTokens
            .FirstOrDefaultAsync(rt => rt.Token == token);

        if (refreshToken == null || !refreshToken.IsActive)
            return false;

        refreshToken.IsRevoked = true;
        refreshToken.RevokedDate = DateTime.UtcNow;
        refreshToken.RevokedByIp = revokedByIp;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> RevokeAllUserTokensAsync(int userId, string revokedByIp)
    {
        var activeTokens = await _context.RefreshTokens
            .Where(rt => rt.NguoiDungId == userId && rt.IsActive)
            .ToListAsync();

        if (!activeTokens.Any())
            return false;

        foreach (var token in activeTokens)
        {
            token.IsRevoked = true;
            token.RevokedDate = DateTime.UtcNow;
            token.RevokedByIp = revokedByIp;
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> CleanupExpiredTokensAsync()
    {
        var expiredTokens = await _context.RefreshTokens
            .Where(rt => rt.ExpiryDate < DateTime.UtcNow)
            .ToListAsync();

        if (!expiredTokens.Any())
            return false;

        foreach (var token in expiredTokens)
        {
            token.DaXoa = true;
        }

        await _context.SaveChangesAsync();
        return true;
    }
}
