export interface User {
  id: number;
  hoTen: string;
  email: string;
  soDienThoai?: string;
  diaChi?: string;
  ngaySinh: string;
  gioiTinh: string;
  soCanCuoc: string;
  ngayDangKy: string;
  trangThaiHoatDong: boolean;
  vaiTro: string;
  ngayTao: string;
  ngayCapNhat?: string;
}

export interface CreateUserRequest {
  hoTen: string;
  email: string;
  soDienThoai?: string;
  diaChi?: string;
  ngaySinh: string;
  gioiTinh: string;
  soCanCuoc: string;
  vaiTro: string;
  matKhau?: string;
}

export interface UpdateUserRequest {
  hoTen: string;
  email: string;
  soDienThoai?: string;
  diaChi?: string;
  ngaySinh: string;
  gioiTinh: string;
  soCanCuoc: string;
  vaiTro: string;
  trangThaiHoatDong: boolean;
}

export interface UserSearchRequest {
  hoTen?: string;
  email?: string;
  soDienThoai?: string;
  vaiTro?: string;
  trangThaiHoatDong?: boolean;
  ngayDangKyTu?: string;
  ngayDangKyDen?: string;
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDescending: boolean;
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  pageSize: number;
}

export interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
}

export const USER_ROLES = [
  { value: 'Admin', label: 'Quản trị viên' },
  { value: 'Librarian', label: 'Thủ thư' },
  { value: 'User', label: 'Người dùng' }
];

export const GENDER_OPTIONS = [
  { value: 'Nam', label: 'Nam' },
  { value: 'Nữ', label: 'Nữ' },
  { value: 'Khác', label: 'Khác' }
];
