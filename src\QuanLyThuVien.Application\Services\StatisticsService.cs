using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;

namespace QuanLyThuVien.Application.Services;

public class StatisticsService : IStatisticsService
{
    private readonly ISachRepository _sachRepository;
    private readonly INguoiDungRepository _nguoiDungRepository;
    private readonly IMuonTraSachRepository _muonTraSachRepository;

    public StatisticsService(
        ISachRepository sachRepository,
        INguoiDungRepository nguoiDungRepository,
        IMuonTraSachRepository muonTraSachRepository)
    {
        _sachRepository = sachRepository;
        _nguoiDungRepository = nguoiDungRepository;
        _muonTraSachRepository = muonTraSachRepository;
    }

    public async Task<DashboardStatisticsDto> GetDashboardStatisticsAsync()
    {
        var totalBooks = await _sachRepository.GetTotalBooksCountAsync();
        var activeUsers = await _nguoiDungRepository.GetActiveUsersCountAsync();
        var currentBorrowings = await _muonTraSachRepository.GetCurrentBorrowingsCountAsync();
        var overdueBorrowings = await _muonTraSachRepository.GetOverdueBorrowingsCountAsync();

        return new DashboardStatisticsDto
        {
            TotalBooks = totalBooks,
            ActiveUsers = activeUsers,
            CurrentBorrowings = currentBorrowings,
            OverdueBorrowings = overdueBorrowings
        };
    }

    public async Task<List<TopBookDto>> GetTopBooksAsync(int count = 10)
    {
        return await _muonTraSachRepository.GetTopBorrowedBooksAsync(count);
    }

    public async Task<List<TopBorrowerDto>> GetTopBorrowersAsync(int count = 10)
    {
        return await _muonTraSachRepository.GetTopBorrowersAsync(count);
    }

    public async Task<List<RecentActivityDto>> GetRecentActivitiesAsync(int count = 20)
    {
        return await _muonTraSachRepository.GetRecentActivitiesAsync(count);
    }
}
