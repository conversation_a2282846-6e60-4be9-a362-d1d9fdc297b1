// using Microsoft.AspNetCore.Authorization; // Temporarily disabled for testing
using Microsoft.AspNetCore.Mvc;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;

namespace QuanLyThuVien.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
// [Authorize(Roles = "Admin,Librarian")] // Temporarily disabled for testing
public class NguoiDungController : ControllerBase
{
    private readonly INguoiDungService _nguoiDungService;

    public NguoiDungController(INguoiDungService nguoiDungService)
    {
        _nguoiDungService = nguoiDungService;
    }

    /// <summary>
    /// Lấy danh sách người dùng với tìm kiếm và phân trang
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<NguoiDungDto>>> GetAll([FromQuery] NguoiDungSearchDto searchDto)
    {
        try
        {
            var result = await _nguoiDungService.GetAllAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy thông tin người dùng theo ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<NguoiDungDto>> GetById(int id)
    {
        try
        {
            var nguoiDung = await _nguoiDungService.GetByIdAsync(id);
            if (nguoiDung == null)
            {
                return NotFound(new { message = $"Không tìm thấy người dùng với ID: {id}" });
            }

            return Ok(nguoiDung);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy thông tin người dùng theo email
    /// </summary>
    [HttpGet("by-email/{email}")]
    public async Task<ActionResult<NguoiDungDto>> GetByEmail(string email)
    {
        try
        {
            var nguoiDung = await _nguoiDungService.GetByEmailAsync(email);
            if (nguoiDung == null)
            {
                return NotFound(new { message = $"Không tìm thấy người dùng với email: {email}" });
            }

            return Ok(nguoiDung);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Tạo người dùng mới
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<NguoiDungDto>> Create([FromBody] CreateNguoiDungDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var nguoiDung = await _nguoiDungService.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { id = nguoiDung.Id }, nguoiDung);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Cập nhật thông tin người dùng
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<NguoiDungDto>> Update(int id, [FromBody] UpdateNguoiDungDto updateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var nguoiDung = await _nguoiDungService.UpdateAsync(id, updateDto);
            return Ok(nguoiDung);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Xóa người dùng (soft delete)
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        try
        {
            var result = await _nguoiDungService.DeleteAsync(id);
            if (result)
            {
                return Ok(new { message = "Xóa người dùng thành công" });
            }
            return BadRequest(new { message = "Không thể xóa người dùng" });
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Đổi mật khẩu người dùng
    /// </summary>
    [HttpPost("{id}/change-password")]
    public async Task<ActionResult> ChangePassword(int id, [FromBody] ChangePasswordDto changePasswordDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _nguoiDungService.ChangePasswordAsync(id, changePasswordDto);
            if (result)
            {
                return Ok(new { message = "Đổi mật khẩu thành công" });
            }
            return BadRequest(new { message = "Không thể đổi mật khẩu" });
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy danh sách người dùng đang hoạt động
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<List<NguoiDungDto>>> GetActiveUsers()
    {
        try
        {
            var users = await _nguoiDungService.GetActiveUsersAsync();
            return Ok(users);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy thống kê số lượng người dùng
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult> GetStatistics()
    {
        try
        {
            var totalUsers = await _nguoiDungService.GetTotalUsersCountAsync();
            var activeUsers = await _nguoiDungService.GetActiveUsersCountAsync();

            return Ok(new
            {
                totalUsers,
                activeUsers,
                inactiveUsers = totalUsers - activeUsers
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }
}
