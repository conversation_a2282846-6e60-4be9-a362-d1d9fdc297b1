using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Interfaces;

public interface IMuonTraSachRepository
{
    Task<PagedResult<MuonTraSach>> GetAllAsync(MuonTraSachSearchDto searchDto);
    Task<MuonTraSach?> GetByIdAsync(int id);
    Task<List<MuonTraSach>> GetByNguoiDungIdAsync(int nguoiDungId);
    Task<List<MuonTraSach>> GetBySachIdAsync(int sachId);
    Task<List<MuonTraSach>> GetActiveBorrowingsByNguoiDungIdAsync(int nguoiDungId);
    Task<List<MuonTraSach>> GetOverdueBorrowingsAsync();
    Task<MuonTraSach> CreateAsync(MuonTraSach muonTraSach);
    Task<MuonTraSach> UpdateAsync(MuonTraSach muonTraSach);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
    Task<bool> CanBorrowBookAsync(int nguoiDungId, int sachId);
    Task<bool> HasActiveBorrowingAsync(int nguoiDungId, int sachId);
    Task<int> GetActiveBorrowingsCountByNguoiDungAsync(int nguoiDungId);
    Task<ThongKeMuonTraDto> GetStatisticsAsync();
    Task<List<MuonTraSach>> GetBorrowingHistoryAsync(int nguoiDungId, int page = 1, int pageSize = 10);
    Task<bool> UpdateOverdueStatusAsync();
    Task<int> GetCurrentBorrowingsCountAsync();
    Task<int> GetOverdueBorrowingsCountAsync();
    Task<List<TopBookDto>> GetTopBorrowedBooksAsync(int count);
    Task<List<TopBorrowerDto>> GetTopBorrowersAsync(int count);
    Task<List<RecentActivityDto>> GetRecentActivitiesAsync(int count);
}
