using System.ComponentModel.DataAnnotations;

namespace QuanLyThuVien.Domain.Entities;

public class RefreshToken : BaseEntity
{
    [Required]
    public string Token { get; set; } = string.Empty;

    [Required]
    public int NguoiDungId { get; set; }

    public DateTime ExpiryDate { get; set; }

    public bool IsRevoked { get; set; } = false;

    public string? ReplacedByToken { get; set; }

    public DateTime? RevokedDate { get; set; }

    public string? RevokedByIp { get; set; }

    public string CreatedByIp { get; set; } = string.Empty;

    // Navigation property
    public virtual NguoiDung NguoiDung { get; set; } = null!;

    // Helper properties
    public bool IsExpired => DateTime.UtcNow >= ExpiryDate;
    public bool IsActive => !IsRevoked && !IsExpired;
}
