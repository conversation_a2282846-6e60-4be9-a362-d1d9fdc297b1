using QuanLyThuVien.Application.DTOs;

namespace QuanLyThuVien.Application.Interfaces;

public interface INguoiDungService
{
    Task<PagedResult<NguoiDungDto>> GetAllAsync(NguoiDungSearchDto searchDto);
    Task<NguoiDungDto?> GetByIdAsync(int id);
    Task<NguoiDungDto?> GetByEmailAsync(string email);
    Task<NguoiDungDto> CreateAsync(CreateNguoiDungDto createDto);
    Task<NguoiDungDto> UpdateAsync(int id, UpdateNguoiDungDto updateDto);
    Task<bool> DeleteAsync(int id);
    Task<bool> ChangePasswordAsync(int id, ChangePasswordDto changePasswordDto);
    Task<List<NguoiDungDto>> GetActiveUsersAsync();
    Task<bool> ValidatePasswordAsync(int id, string password);
    Task<int> GetTotalUsersCountAsync();
    Task<int> GetActiveUsersCountAsync();
}
