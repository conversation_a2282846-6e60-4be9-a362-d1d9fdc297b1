using QuanLyThuVien.Application.DTOs;

namespace QuanLyThuVien.Application.Interfaces;

public interface IAuthService
{
    Task<TokenResponseDto> LoginAsync(LoginDto loginDto, string ipAddress);
    Task<TokenResponseDto> RegisterAsync(RegisterDto registerDto, string ipAddress);
    Task<TokenResponseDto> RefreshTokenAsync(string refreshToken, string ipAddress);
    Task<bool> RevokeTokenAsync(string refreshToken, string ipAddress);
    Task<bool> RevokeAllTokensAsync(int userId, string ipAddress);
    Task<bool> ChangePasswordAsync(int userId, ChangePasswordRequestDto changePasswordDto);
    Task<UserInfoDto?> GetCurrentUserAsync(int userId);
    Task<bool> ValidateRefreshTokenAsync(string refreshToken);
    Task<bool> LogoutAsync(string refreshToken, string ipAddress);
}
