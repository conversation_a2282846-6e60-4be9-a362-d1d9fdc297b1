using System.ComponentModel.DataAnnotations;

namespace QuanLyThuVien.Domain.Entities;

public class NguoiDung : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string HoTen { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [MaxLength(15)]
    public string? SoDienThoai { get; set; }

    [MaxLength(200)]
    public string? Dia<PERSON>hi { get; set; }

    public DateTime NgaySinh { get; set; }

    [MaxLength(10)]
    public string GioiTinh { get; set; } = string.Empty;

    [MaxLength(20)]
    public string SoCanCuoc { get; set; } = string.Empty;

    public DateTime NgayDangKy { get; set; } = DateTime.Now;

    public bool TrangThaiHoatDong { get; set; } = true;

    [MaxLength(20)]
    public string VaiTro { get; set; } = "User"; // User, Admin

    [MaxLength(255)]
    public string? MatKhauHash { get; set; }

    // Navigation properties
    public virtual ICollection<MuonTraSach> DanhSachMuonTra { get; set; } = new List<MuonTraSach>();
    public virtual ICollection<RefreshToken> RefreshTokens { get; set; } = new List<RefreshToken>();
}
