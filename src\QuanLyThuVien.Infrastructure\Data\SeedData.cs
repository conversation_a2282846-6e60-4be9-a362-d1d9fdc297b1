using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Infrastructure.Data;

public static class SeedData
{
    public static async Task SeedAsync(QuanLyThuVienDbContext context)
    {
        if (context.Sach.Any()) return; // Đã có dữ liệu

        // Seed NguoiDung
        await SeedNguoiDungAsync(context);

        // Seed Sach
        await SeedSachAsync(context);
    }

    private static async Task SeedNguoiDungAsync(QuanLyThuVienDbContext context)
    {
        var nguoiDungList = new List<NguoiDung>
        {
            new NguoiDung
            {
                HoTen = "Admin System",
                Email = "<EMAIL>",
                SoDienThoai = "0123456789",
                DiaChi = "123 Đường ABC, Quận 1, TP.HCM",
                NgaySinh = new DateTime(1990, 1, 1),
                GioiTinh = "Nam",
                SoCanCuoc = "123456789012",
                VaiTro = "Admin",
                TrangT<PERSON>HoatDong = true,
                MatKhauHash = BCrypt.Net.BCrypt.HashPassword("admin123") // Password: admin123
            },
            new NguoiDung
            {
                HoTen = "Th<PERSON> thư Nguyễn Văn A",
                Email = "<EMAIL>",
                SoDienThoai = "0987654321",
                DiaChi = "456 Đường XYZ, Quận 2, TP.HCM",
                NgaySinh = new DateTime(1985, 5, 15),
                GioiTinh = "Nam",
                SoCanCuoc = "987654321098",
                VaiTro = "Librarian",
                TrangThaiHoatDong = true,
                MatKhauHash = BCrypt.Net.BCrypt.HashPassword("librarian123") // Password: librarian123
            },
            new NguoiDung
            {
                HoTen = "Người dùng Trần Thị B",
                Email = "<EMAIL>",
                SoDienThoai = "0369852147",
                DiaChi = "789 Đường DEF, Quận 3, TP.HCM",
                NgaySinh = new DateTime(1995, 8, 20),
                GioiTinh = "Nữ",
                SoCanCuoc = "147258369012",
                VaiTro = "User",
                TrangThaiHoatDong = true,
                MatKhauHash = BCrypt.Net.BCrypt.HashPassword("user123") // Password: user123
            }
        };

        context.NguoiDung.AddRange(nguoiDungList);
        await context.SaveChangesAsync();
    }

    private static async Task SeedSachAsync(QuanLyThuVienDbContext context)
    {

        var sachList = new List<Sach>
        {
            new Sach
            {
                TenSach = "Lập trình C# từ cơ bản đến nâng cao",
                TacGia = "Nguyễn Văn A",
                NhaXuatBan = "NXB Thông tin và Truyền thông",
                NamXuatBan = 2023,
                ISBN = "978-604-80-1234-5",
                SoTrang = 450,
                TheLoai = "Công nghệ thông tin",
                SoLuongTong = 10,
                SoLuongConLai = 10,
                MoTa = "Cuốn sách hướng dẫn lập trình C# từ cơ bản đến nâng cao với nhiều ví dụ thực tế.",
                Gia = 250000
            },
            new Sach
            {
                TenSach = "Angular Framework - Xây dựng ứng dụng web hiện đại",
                TacGia = "Trần Thị B",
                NhaXuatBan = "NXB Khoa học và Kỹ thuật",
                NamXuatBan = 2023,
                ISBN = "978-604-80-5678-9",
                SoTrang = 380,
                TheLoai = "Công nghệ thông tin",
                SoLuongTong = 8,
                SoLuongConLai = 8,
                MoTa = "Hướng dẫn chi tiết về Angular framework để xây dựng ứng dụng web single-page.",
                Gia = 300000
            },
            new Sach
            {
                TenSach = "Cơ sở dữ liệu SQL Server",
                TacGia = "Lê Văn C",
                NhaXuatBan = "NXB Đại học Quốc gia",
                NamXuatBan = 2022,
                ISBN = "978-604-80-9012-3",
                SoTrang = 520,
                TheLoai = "Cơ sở dữ liệu",
                SoLuongTong = 15,
                SoLuongConLai = 15,
                MoTa = "Giáo trình về cơ sở dữ liệu SQL Server từ cơ bản đến nâng cao.",
                Gia = 280000
            },
            new Sach
            {
                TenSach = "Thiết kế hệ thống phần mềm",
                TacGia = "Phạm Thị D",
                NhaXuatBan = "NXB Giáo dục Việt Nam",
                NamXuatBan = 2023,
                ISBN = "978-604-80-3456-7",
                SoTrang = 420,
                TheLoai = "Kỹ thuật phần mềm",
                SoLuongTong = 12,
                SoLuongConLai = 12,
                MoTa = "Các nguyên lý và phương pháp thiết kế hệ thống phần mềm hiện đại.",
                Gia = 320000
            },
            new Sach
            {
                TenSach = "JavaScript ES6+ và Node.js",
                TacGia = "Hoàng Văn E",
                NhaXuatBan = "NXB Thông tin và Truyền thông",
                NamXuatBan = 2023,
                ISBN = "978-604-80-7890-1",
                SoTrang = 360,
                TheLoai = "Công nghệ thông tin",
                SoLuongTong = 20,
                SoLuongConLai = 20,
                MoTa = "Hướng dẫn lập trình JavaScript hiện đại và phát triển ứng dụng với Node.js.",
                Gia = 270000
            }
        };

        context.Sach.AddRange(sachList);
        await context.SaveChangesAsync();
    }
}
