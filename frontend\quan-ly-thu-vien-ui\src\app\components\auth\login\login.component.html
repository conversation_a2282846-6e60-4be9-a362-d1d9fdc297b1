<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>library_books</mat-icon>
        <PERSON><PERSON><PERSON> nh<PERSON>p
      </mat-card-title>
      <mat-card-subtitle><PERSON><PERSON> th<PERSON> v<PERSON></mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email</mat-label>
          <input 
            matInput 
            type="email" 
            formControlName="email"
            placeholder="Nhập email của bạn"
            autocomplete="email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
            {{ getErrorMessage('email') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>M<PERSON><PERSON> khẩu</mat-label>
          <input 
            matInput 
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="password"
            placeholder="Nhập mật khẩu"
            autocomplete="current-password">
          <button 
            mat-icon-button 
            matSuffix 
            type="button"
            (click)="togglePasswordVisibility()"
            [attr.aria-label]="'Hide password'"
            [attr.aria-pressed]="hidePassword">
            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
            {{ getErrorMessage('password') }}
          </mat-error>
        </mat-form-field>

        <div class="form-options">
          <mat-checkbox formControlName="rememberMe">
            Ghi nhớ đăng nhập
          </mat-checkbox>
        </div>

        <div class="form-actions">
          <button 
            mat-raised-button 
            color="primary" 
            type="submit"
            class="full-width login-button"
            [disabled]="isLoading">
            <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
            <span *ngIf="!isLoading">Đăng nhập</span>
            <span *ngIf="isLoading">Đang đăng nhập...</span>
          </button>
        </div>

        <div class="form-footer">
          <p>
            Chưa có tài khoản? 
            <button 
              mat-button 
              color="primary" 
              type="button"
              (click)="navigateToRegister()">
              Đăng ký ngay
            </button>
          </p>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
