export interface Book {
  id: number;
  tenSach: string;
  tacGia: string;
  nhaXuatBan: string;
  namXuatBan: number;
  isbn: string;
  soTrang: number;
  theLoai: string;
  soLuongTong: number;
  soLuongConLai: number;
  moTa?: string;
  gia: number;
  ngayTao: string;
  ngayCapNhat?: string;
  daXoa: boolean;
}

export interface CreateBookRequest {
  tenSach: string;
  tacGia: string;
  nhaXuatBan: string;
  namXuatBan: number;
  isbn: string;
  soTrang: number;
  theLoai: string;
  soLuongTong: number;
  moTa?: string;
  gia: number;
}

export interface UpdateBookRequest {
  tenSach: string;
  tacGia: string;
  nhaXuatBan: string;
  namXuatBan: number;
  isbn: string;
  soTrang: number;
  theLoai: string;
  soLuongTong: number;
  moTa?: string;
  gia: number;
}

export interface BookSearchRequest {
  tenSach?: string;
  tacGia?: string;
  nhaXuatBan?: string;
  theLoai?: string;
  isbn?: string;
  namXuatBanTu?: number;
  namXuatBanDen?: number;
  giaTu?: number;
  giaDen?: number;
  soLuongConLai?: number;
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDescending: boolean;
}

export interface BookStatistics {
  totalBooks: number;
  availableBooks: number;
  borrowedBooks: number;
  totalValue: number;
  categoriesCount: number;
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  pageSize: number;
}

export const BOOK_CATEGORIES = [
  { value: 'Công nghệ thông tin', label: 'Công nghệ thông tin' },
  { value: 'Cơ sở dữ liệu', label: 'Cơ sở dữ liệu' },
  { value: 'Kỹ thuật phần mềm', label: 'Kỹ thuật phần mềm' },
  { value: 'Mạng máy tính', label: 'Mạng máy tính' },
  { value: 'Trí tuệ nhân tạo', label: 'Trí tuệ nhân tạo' },
  { value: 'Văn học', label: 'Văn học' },
  { value: 'Khoa học', label: 'Khoa học' },
  { value: 'Lịch sử', label: 'Lịch sử' },
  { value: 'Triết học', label: 'Triết học' },
  { value: 'Kinh tế', label: 'Kinh tế' },
  { value: 'Tâm lý học', label: 'Tâm lý học' },
  { value: 'Giáo dục', label: 'Giáo dục' },
  { value: 'Y học', label: 'Y học' },
  { value: 'Luật', label: 'Luật' },
  { value: 'Nghệ thuật', label: 'Nghệ thuật' },
  { value: 'Thể thao', label: 'Thể thao' },
  { value: 'Du lịch', label: 'Du lịch' },
  { value: 'Nấu ăn', label: 'Nấu ăn' },
  { value: 'Khác', label: 'Khác' }
];

export const PUBLISHERS = [
  { value: 'NXB Thông tin và Truyền thông', label: 'NXB Thông tin và Truyền thông' },
  { value: 'NXB Khoa học và Kỹ thuật', label: 'NXB Khoa học và Kỹ thuật' },
  { value: 'NXB Đại học Quốc gia', label: 'NXB Đại học Quốc gia' },
  { value: 'NXB Giáo dục Việt Nam', label: 'NXB Giáo dục Việt Nam' },
  { value: 'NXB Văn học', label: 'NXB Văn học' },
  { value: 'NXB Kim Đồng', label: 'NXB Kim Đồng' },
  { value: 'NXB Trẻ', label: 'NXB Trẻ' },
  { value: 'NXB Lao động', label: 'NXB Lao động' },
  { value: 'NXB Tổng hợp TP.HCM', label: 'NXB Tổng hợp TP.HCM' },
  { value: 'NXB Hà Nội', label: 'NXB Hà Nội' },
  { value: 'Khác', label: 'Khác' }
];

export const SORT_OPTIONS = [
  { value: 'tenSach', label: 'Tên sách' },
  { value: 'tacGia', label: 'Tác giả' },
  { value: 'namXuatBan', label: 'Năm xuất bản' },
  { value: 'gia', label: 'Giá' },
  { value: 'soLuongConLai', label: 'Số lượng còn lại' },
  { value: 'ngayTao', label: 'Ngày tạo' }
];
