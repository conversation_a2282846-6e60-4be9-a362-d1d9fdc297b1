import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { BookService } from '../../../services/book.service';
import { AuthService } from '../../../services/auth.service';
import { Book, CreateBookRequest, UpdateBookRequest, BOOK_CATEGORIES, PUBLISHERS } from '../../../models/book.model';

@Component({
  selector: 'app-book-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <div class="book-form-container">
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
            {{ isEditMode ? 'Chỉnh sửa sách' : 'Thêm sách mới' }}
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="bookForm" (ngSubmit)="onSubmit()" class="book-form">
            <div class="form-section">
              <h3>Thông tin cơ bản</h3>
              
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Tên sách *</mat-label>
                  <input matInput formControlName="tenSach" placeholder="Nhập tên sách">
                  <mat-icon matSuffix>book</mat-icon>
                  <mat-error *ngIf="bookForm.get('tenSach')?.invalid && bookForm.get('tenSach')?.touched">
                    {{ getErrorMessage('tenSach') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row two-columns">
                <mat-form-field appearance="outline">
                  <mat-label>Tác giả *</mat-label>
                  <input matInput formControlName="tacGia" placeholder="Nhập tên tác giả">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="bookForm.get('tacGia')?.invalid && bookForm.get('tacGia')?.touched">
                    {{ getErrorMessage('tacGia') }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>ISBN *</mat-label>
                  <input matInput formControlName="isbn" placeholder="978-604-80-1234-5">
                  <mat-icon matSuffix>qr_code</mat-icon>
                  <mat-error *ngIf="bookForm.get('isbn')?.invalid && bookForm.get('isbn')?.touched">
                    {{ getErrorMessage('isbn') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row two-columns">
                <mat-form-field appearance="outline">
                  <mat-label>Nhà xuất bản *</mat-label>
                  <mat-select formControlName="nhaXuatBan">
                    <mat-option *ngFor="let publisher of publishers" [value]="publisher.value">
                      {{ publisher.label }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="bookForm.get('nhaXuatBan')?.invalid && bookForm.get('nhaXuatBan')?.touched">
                    {{ getErrorMessage('nhaXuatBan') }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Thể loại *</mat-label>
                  <mat-select formControlName="theLoai">
                    <mat-option *ngFor="let category of bookCategories" [value]="category.value">
                      {{ category.label }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="bookForm.get('theLoai')?.invalid && bookForm.get('theLoai')?.touched">
                    {{ getErrorMessage('theLoai') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row three-columns">
                <mat-form-field appearance="outline">
                  <mat-label>Năm xuất bản *</mat-label>
                  <input matInput type="number" formControlName="namXuatBan" placeholder="2024">
                  <mat-icon matSuffix>calendar_today</mat-icon>
                  <mat-error *ngIf="bookForm.get('namXuatBan')?.invalid && bookForm.get('namXuatBan')?.touched">
                    {{ getErrorMessage('namXuatBan') }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Số trang *</mat-label>
                  <input matInput type="number" formControlName="soTrang" placeholder="300">
                  <mat-icon matSuffix>description</mat-icon>
                  <mat-error *ngIf="bookForm.get('soTrang')?.invalid && bookForm.get('soTrang')?.touched">
                    {{ getErrorMessage('soTrang') }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Số lượng *</mat-label>
                  <input matInput type="number" formControlName="soLuongTong" placeholder="10">
                  <mat-icon matSuffix>inventory</mat-icon>
                  <mat-error *ngIf="bookForm.get('soLuongTong')?.invalid && bookForm.get('soLuongTong')?.touched">
                    {{ getErrorMessage('soLuongTong') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Giá *</mat-label>
                  <input matInput type="number" formControlName="gia" placeholder="250000">
                  <span matTextPrefix>₫ </span>
                  <mat-icon matSuffix>attach_money</mat-icon>
                  <mat-error *ngIf="bookForm.get('gia')?.invalid && bookForm.get('gia')?.touched">
                    {{ getErrorMessage('gia') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Mô tả</mat-label>
                  <textarea matInput formControlName="moTa" rows="4" placeholder="Nhập mô tả về sách..."></textarea>
                  <mat-icon matSuffix>notes</mat-icon>
                  <mat-error *ngIf="bookForm.get('moTa')?.invalid && bookForm.get('moTa')?.touched">
                    {{ getErrorMessage('moTa') }}
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="onCancel()" [disabled]="isLoading">
                <mat-icon>cancel</mat-icon>
                Hủy
              </button>
              <button mat-raised-button color="primary" type="submit" [disabled]="bookForm.invalid || isLoading">
                <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
                <mat-icon *ngIf="!isLoading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
                {{ isLoading ? 'Đang xử lý...' : (isEditMode ? 'Cập nhật' : 'Thêm mới') }}
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .book-form-container {
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
    }

    .form-card {
      width: 100%;
    }

    .form-card .mat-mdc-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
    }

    .book-form {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .form-section {
      h3 {
        margin: 0 0 16px 0;
        color: #333;
        font-weight: 500;
        border-bottom: 2px solid #e0e0e0;
        padding-bottom: 8px;
      }
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      &.two-columns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
      }

      &.three-columns {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 16px;
      }
    }

    .full-width {
      width: 100%;
    }

    .form-actions {
      display: flex;
      gap: 16px;
      justify-content: flex-end;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #e0e0e0;
    }

    .form-actions button {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 120px;
      height: 48px;
    }

    .button-spinner {
      margin-right: 8px;
    }

    @media (max-width: 768px) {
      .book-form-container {
        padding: 16px;
      }

      .form-row {
        &.two-columns,
        &.three-columns {
          grid-template-columns: 1fr;
        }
      }

      .form-actions {
        flex-direction: column-reverse;

        button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  `]
})
export class BookFormComponent implements OnInit {
  bookForm!: FormGroup;
  isEditMode = false;
  isLoading = false;
  bookId: number | null = null;

  bookCategories = BOOK_CATEGORIES;
  publishers = PUBLISHERS;

  constructor(
    private formBuilder: FormBuilder,
    private bookService: BookService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.checkEditMode();
  }

  private initializeForm(): void {
    const currentYear = new Date().getFullYear();
    
    this.bookForm = this.formBuilder.group({
      tenSach: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(200)]],
      tacGia: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      isbn: ['', [Validators.required, Validators.pattern(/^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/)]],
      nhaXuatBan: ['', [Validators.required]],
      theLoai: ['', [Validators.required]],
      namXuatBan: ['', [Validators.required, Validators.min(1900), Validators.max(currentYear + 1)]],
      soTrang: ['', [Validators.required, Validators.min(1), Validators.max(10000)]],
      soLuongTong: ['', [Validators.required, Validators.min(1), Validators.max(1000)]],
      gia: ['', [Validators.required, Validators.min(0), Validators.max(10000000)]],
      moTa: ['', [Validators.maxLength(1000)]]
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.bookId = parseInt(id, 10);
      this.loadBook();
    }
  }

  private loadBook(): void {
    if (!this.bookId) return;

    this.isLoading = true;
    this.bookService.getBookById(this.bookId).subscribe({
      next: (book) => {
        this.populateForm(book);
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi tải thông tin sách', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/books']);
      }
    });
  }

  private populateForm(book: Book): void {
    this.bookForm.patchValue({
      tenSach: book.tenSach,
      tacGia: book.tacGia,
      isbn: book.isbn,
      nhaXuatBan: book.nhaXuatBan,
      theLoai: book.theLoai,
      namXuatBan: book.namXuatBan,
      soTrang: book.soTrang,
      soLuongTong: book.soLuongTong,
      gia: book.gia,
      moTa: book.moTa
    });
  }

  onSubmit(): void {
    if (this.bookForm.valid && !this.isLoading) {
      this.isLoading = true;

      if (this.isEditMode) {
        this.updateBook();
      } else {
        this.createBook();
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private createBook(): void {
    const formValue = this.bookForm.value;
    const createRequest: CreateBookRequest = {
      tenSach: formValue.tenSach,
      tacGia: formValue.tacGia,
      isbn: formValue.isbn,
      nhaXuatBan: formValue.nhaXuatBan,
      theLoai: formValue.theLoai,
      namXuatBan: formValue.namXuatBan,
      soTrang: formValue.soTrang,
      soLuongTong: formValue.soLuongTong,
      gia: formValue.gia,
      moTa: formValue.moTa
    };

    this.bookService.createBook(createRequest).subscribe({
      next: () => {
        this.isLoading = false;
        this.snackBar.open('Thêm sách thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/books']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi thêm sách', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private updateBook(): void {
    if (!this.bookId) return;

    const formValue = this.bookForm.value;
    const updateRequest: UpdateBookRequest = {
      tenSach: formValue.tenSach,
      tacGia: formValue.tacGia,
      isbn: formValue.isbn,
      nhaXuatBan: formValue.nhaXuatBan,
      theLoai: formValue.theLoai,
      namXuatBan: formValue.namXuatBan,
      soTrang: formValue.soTrang,
      soLuongTong: formValue.soLuongTong,
      gia: formValue.gia,
      moTa: formValue.moTa
    };

    this.bookService.updateBook(this.bookId, updateRequest).subscribe({
      next: () => {
        this.isLoading = false;
        this.snackBar.open('Cập nhật sách thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/books']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.message || 'Lỗi khi cập nhật sách', 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/books']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.bookForm.controls).forEach(key => {
      const control = this.bookForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.bookForm.get(fieldName);
    
    if (control?.hasError('required')) {
      return `${this.getFieldDisplayName(fieldName)} là bắt buộc`;
    }
    
    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength']?.requiredLength;
      return `${this.getFieldDisplayName(fieldName)} phải có ít nhất ${minLength} ký tự`;
    }
    
    if (control?.hasError('maxlength')) {
      const maxLength = control.errors?.['maxlength']?.requiredLength;
      return `${this.getFieldDisplayName(fieldName)} không được vượt quá ${maxLength} ký tự`;
    }
    
    if (control?.hasError('min')) {
      const min = control.errors?.['min']?.min;
      return `${this.getFieldDisplayName(fieldName)} phải lớn hơn hoặc bằng ${min}`;
    }
    
    if (control?.hasError('max')) {
      const max = control.errors?.['max']?.max;
      return `${this.getFieldDisplayName(fieldName)} phải nhỏ hơn hoặc bằng ${max}`;
    }
    
    if (control?.hasError('pattern')) {
      if (fieldName === 'isbn') {
        return 'ISBN không hợp lệ';
      }
    }
    
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      tenSach: 'Tên sách',
      tacGia: 'Tác giả',
      isbn: 'ISBN',
      nhaXuatBan: 'Nhà xuất bản',
      theLoai: 'Thể loại',
      namXuatBan: 'Năm xuất bản',
      soTrang: 'Số trang',
      soLuongTong: 'Số lượng',
      gia: 'Giá',
      moTa: 'Mô tả'
    };
    return fieldNames[fieldName] || fieldName;
  }
}
