export interface Borrowing {
  id: number;
  nguoiDungId: number;
  sachId: number;
  ngayMuon: string;
  ngayHenTra: string;
  ngayTraThucTe?: string;
  trangThai: BorrowingStatus;
  ghiChu?: string;
  ngayTao: string;
  ngayCapNhat?: string;
  
  // Navigation properties
  nguoiDung?: {
    id: number;
    hoTen: string;
    email: string;
    soDienThoai?: string;
  };
  sach?: {
    id: number;
    tenSach: string;
    tacGia: string;
    isbn: string;
  };
}

export interface CreateBorrowingRequest {
  nguoiDungId: number;
  sachId: number;
  ngayHenTra: string;
  ghiChu?: string;
}

export interface UpdateBorrowingRequest {
  ngayHenTra: string;
  ghiChu?: string;
}

export interface ReturnBookRequest {
  ngayTraThucTe: string;
  ghiChu?: string;
}

export interface BorrowingSearchRequest {
  nguoiDungId?: number;
  sachId?: number;
  trangThai?: BorrowingStatus;
  ngayMuonTu?: string;
  ngayMuonDen?: string;
  ngayHenTraTu?: string;
  ngayHenTraDen?: string;
  isOverdue?: boolean;
  hoTenNguoiDung?: string;
  tenSach?: string;
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDescending: boolean;
}

export interface BorrowingStatistics {
  totalBorrowings: number;
  activeBorrowings: number;
  overdueBorrowings: number;
  returnedBorrowings: number;
  averageBorrowingDays: number;
  mostBorrowedBooks: {
    sachId: number;
    tenSach: string;
    borrowCount: number;
  }[];
  topBorrowers: {
    nguoiDungId: number;
    hoTen: string;
    borrowCount: number;
  }[];
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  pageSize: number;
}

export enum BorrowingStatus {
  BORROWED = 'Borrowed',
  RETURNED = 'Returned',
  OVERDUE = 'Overdue',
  LOST = 'Lost'
}

export const BORROWING_STATUS_OPTIONS = [
  { value: BorrowingStatus.BORROWED, label: 'Đang mượn', color: 'primary' },
  { value: BorrowingStatus.RETURNED, label: 'Đã trả', color: 'success' },
  { value: BorrowingStatus.OVERDUE, label: 'Quá hạn', color: 'warn' },
  { value: BorrowingStatus.LOST, label: 'Mất sách', color: 'danger' }
];

export const SORT_OPTIONS = [
  { value: 'ngayMuon', label: 'Ngày mượn' },
  { value: 'ngayHenTra', label: 'Ngày hẹn trả' },
  { value: 'ngayTraThucTe', label: 'Ngày trả thực tế' },
  { value: 'hoTenNguoiDung', label: 'Tên người mượn' },
  { value: 'tenSach', label: 'Tên sách' },
  { value: 'trangThai', label: 'Trạng thái' }
];

export const DEFAULT_BORROWING_DAYS = 14; // 2 weeks default borrowing period

export interface BorrowingValidation {
  canBorrow: boolean;
  reason?: string;
  maxBooksAllowed: number;
  currentBorrowedCount: number;
  hasOverdueBooks: boolean;
}

export interface QuickBorrowingInfo {
  availableBooks: {
    id: number;
    tenSach: string;
    tacGia: string;
    soLuongConLai: number;
  }[];
  activeUsers: {
    id: number;
    hoTen: string;
    email: string;
    currentBorrowedCount: number;
  }[];
}
