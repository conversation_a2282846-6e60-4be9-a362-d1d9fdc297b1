import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { 
  Book, 
  CreateBookRequest, 
  UpdateBookRequest, 
  BookSearchRequest, 
  PagedResult,
  BookStatistics 
} from '../models/book.model';

@Injectable({
  providedIn: 'root'
})
export class BookService {
  private readonly API_URL = 'http://localhost:5247/api';

  constructor(private http: HttpClient) {}

  getBooks(searchRequest: BookSearchRequest): Observable<PagedResult<Book>> {
    let params = new HttpParams()
      .set('page', searchRequest.page.toString())
      .set('pageSize', searchRequest.pageSize.toString())
      .set('sortDescending', searchRequest.sortDescending.toString());

    if (searchRequest.tenSach) {
      params = params.set('tenSach', searchRequest.tenSach);
    }
    if (searchRequest.tacGia) {
      params = params.set('tacGia', searchRequest.tacGia);
    }
    if (searchRequest.nhaXuatBan) {
      params = params.set('nhaXuatBan', searchRequest.nhaXuatBan);
    }
    if (searchRequest.theLoai) {
      params = params.set('theLoai', searchRequest.theLoai);
    }
    if (searchRequest.isbn) {
      params = params.set('isbn', searchRequest.isbn);
    }
    if (searchRequest.namXuatBanTu) {
      params = params.set('namXuatBanTu', searchRequest.namXuatBanTu.toString());
    }
    if (searchRequest.namXuatBanDen) {
      params = params.set('namXuatBanDen', searchRequest.namXuatBanDen.toString());
    }
    if (searchRequest.giaTu) {
      params = params.set('giaTu', searchRequest.giaTu.toString());
    }
    if (searchRequest.giaDen) {
      params = params.set('giaDen', searchRequest.giaDen.toString());
    }
    if (searchRequest.soLuongConLai !== undefined) {
      params = params.set('soLuongConLai', searchRequest.soLuongConLai.toString());
    }
    if (searchRequest.sortBy) {
      params = params.set('sortBy', searchRequest.sortBy);
    }

    return this.http.get<PagedResult<Book>>(`${this.API_URL}/sach`, { params })
      .pipe(catchError(this.handleError));
  }

  getBookById(id: number): Observable<Book> {
    return this.http.get<Book>(`${this.API_URL}/sach/${id}`)
      .pipe(catchError(this.handleError));
  }

  getBookByIsbn(isbn: string): Observable<Book> {
    return this.http.get<Book>(`${this.API_URL}/sach/by-isbn/${encodeURIComponent(isbn)}`)
      .pipe(catchError(this.handleError));
  }

  createBook(book: CreateBookRequest): Observable<Book> {
    return this.http.post<Book>(`${this.API_URL}/sach`, book)
      .pipe(catchError(this.handleError));
  }

  updateBook(id: number, book: UpdateBookRequest): Observable<Book> {
    return this.http.put<Book>(`${this.API_URL}/sach/${id}`, book)
      .pipe(catchError(this.handleError));
  }

  deleteBook(id: number): Observable<any> {
    return this.http.delete(`${this.API_URL}/sach/${id}`)
      .pipe(catchError(this.handleError));
  }

  getAvailableBooks(): Observable<Book[]> {
    return this.http.get<Book[]>(`${this.API_URL}/sach/available`)
      .pipe(catchError(this.handleError));
  }

  getBooksByCategory(category: string): Observable<Book[]> {
    return this.http.get<Book[]>(`${this.API_URL}/sach/by-category/${encodeURIComponent(category)}`)
      .pipe(catchError(this.handleError));
  }

  getBookStatistics(): Observable<BookStatistics> {
    return this.http.get<BookStatistics>(`${this.API_URL}/sach/statistics`)
      .pipe(catchError(this.handleError));
  }

  searchBooks(query: string): Observable<Book[]> {
    const params = new HttpParams().set('q', query);
    return this.http.get<Book[]>(`${this.API_URL}/sach/search`, { params })
      .pipe(catchError(this.handleError));
  }

  getPopularBooks(limit: number = 10): Observable<Book[]> {
    const params = new HttpParams().set('limit', limit.toString());
    return this.http.get<Book[]>(`${this.API_URL}/sach/popular`, { params })
      .pipe(catchError(this.handleError));
  }

  getRecentBooks(limit: number = 10): Observable<Book[]> {
    const params = new HttpParams().set('limit', limit.toString());
    return this.http.get<Book[]>(`${this.API_URL}/sach/recent`, { params })
      .pipe(catchError(this.handleError));
  }

  private handleError(error: any): Observable<never> {
    let errorMessage = 'Đã xảy ra lỗi không xác định';
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (typeof error.error === 'string') {
      errorMessage = error.error;
    }
    
    console.error('Book Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
