import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { AuthService } from '../../services/auth.service';
import { UserInfo } from '../../models/auth.model';

interface DashboardCard {
  title: string;
  value: number | string;
  icon: string;
  color: string;
  route?: string;
  loading?: boolean;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatGridListModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>Dashboard</h1>
        <p *ngIf="currentUser">Chào mừng, {{ currentUser.hoTen }}!</p>
      </div>

      <div class="dashboard-grid">
        <mat-grid-list [cols]="getColumns()" rowHeight="200px" gutterSize="20px">
          <mat-grid-tile *ngFor="let card of dashboardCards">
            <mat-card class="dashboard-card" [ngClass]="card.color">
              <mat-card-content>
                <div class="card-header">
                  <mat-icon class="card-icon">{{ card.icon }}</mat-icon>
                  <div class="card-value">
                    <span *ngIf="!card.loading">{{ card.value }}</span>
                    <mat-spinner *ngIf="card.loading" diameter="30"></mat-spinner>
                  </div>
                </div>
                <div class="card-title">{{ card.title }}</div>
                <div class="card-actions" *ngIf="card.route">
                  <button mat-button [routerLink]="card.route" class="view-more-btn">
                    Xem chi tiết
                    <mat-icon>arrow_forward</mat-icon>
                  </button>
                </div>
              </mat-card-content>
            </mat-card>
          </mat-grid-tile>
        </mat-grid-list>
      </div>

      <div class="quick-actions" *ngIf="canPerformActions()">
        <h2>Thao tác nhanh</h2>
        <div class="action-buttons">
          <button mat-raised-button color="primary" routerLink="/books/add" *ngIf="canManageBooks()">
            <mat-icon>add</mat-icon>
            Thêm sách mới
          </button>
          <button mat-raised-button color="accent" routerLink="/borrowings/add">
            <mat-icon>assignment_add</mat-icon>
            Mượn sách
          </button>
          <button mat-raised-button routerLink="/users/add" *ngIf="canManageUsers()">
            <mat-icon>person_add</mat-icon>
            Thêm người dùng
          </button>
        </div>
      </div>

      <div class="recent-activities">
        <h2>Hoạt động gần đây</h2>
        <mat-card class="activity-card">
          <mat-card-content>
            <div class="activity-placeholder">
              <mat-icon>history</mat-icon>
              <p>Chức năng hiển thị hoạt động gần đây sẽ được phát triển trong phiên bản tiếp theo.</p>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .dashboard-header {
      margin-bottom: 30px;
    }

    .dashboard-header h1 {
      margin: 0 0 8px 0;
      color: #333;
      font-weight: 500;
    }

    .dashboard-header p {
      margin: 0;
      color: #666;
      font-size: 16px;
    }

    .dashboard-grid {
      margin-bottom: 40px;
    }

    .dashboard-card {
      width: 100%;
      height: 100%;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .dashboard-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .dashboard-card.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .dashboard-card.accent {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
    }

    .dashboard-card.warn {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
    }

    .dashboard-card.success {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      color: white;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .card-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
    }

    .card-value {
      font-size: 32px;
      font-weight: bold;
      text-align: right;
    }

    .card-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;
    }

    .view-more-btn {
      color: inherit;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .quick-actions {
      margin-bottom: 40px;
    }

    .quick-actions h2 {
      margin-bottom: 20px;
      color: #333;
      font-weight: 500;
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }

    .action-buttons button {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 24px;
    }

    .recent-activities h2 {
      margin-bottom: 20px;
      color: #333;
      font-weight: 500;
    }

    .activity-card {
      min-height: 200px;
    }

    .activity-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 150px;
      color: #999;
      text-align: center;
    }

    .activity-placeholder mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    @media (max-width: 768px) {
      .dashboard-container {
        padding: 16px;
      }
      
      .action-buttons {
        flex-direction: column;
      }
      
      .action-buttons button {
        width: 100%;
        justify-content: center;
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  currentUser: UserInfo | null = null;
  
  dashboardCards: DashboardCard[] = [
    {
      title: 'Tổng số sách',
      value: '---',
      icon: 'book',
      color: 'primary',
      route: '/books',
      loading: true
    },
    {
      title: 'Người dùng hoạt động',
      value: '---',
      icon: 'people',
      color: 'accent',
      route: '/users',
      loading: true
    },
    {
      title: 'Sách đang mượn',
      value: '---',
      icon: 'assignment',
      color: 'warn',
      route: '/borrowings',
      loading: true
    },
    {
      title: 'Sách quá hạn',
      value: '---',
      icon: 'warning',
      color: 'success',
      route: '/borrowings',
      loading: true
    }
  ];

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });

    // Simulate loading data
    setTimeout(() => {
      this.dashboardCards.forEach(card => {
        card.loading = false;
        card.value = Math.floor(Math.random() * 1000);
      });
    }, 2000);
  }

  getColumns(): number {
    if (window.innerWidth < 768) return 1;
    if (window.innerWidth < 1024) return 2;
    return 4;
  }

  canPerformActions(): boolean {
    return this.currentUser !== null;
  }

  canManageBooks(): boolean {
    return this.authService.canManageBooks();
  }

  canManageUsers(): boolean {
    return this.authService.canManageUsers();
  }
}
